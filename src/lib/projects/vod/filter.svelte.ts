import { format } from 'date-fns/format'

import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { type TimeUnit, type Iso3166Alpha2, AsyncFilter } from '$lib/graphql/generated/gateway'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, timeUnitFilter } from '../common.filters.svelte'

import { filter, filteredProviders } from '$lib/components/Project/project.service'

export interface VodFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	extraCardsByProvider: string
	ampereVodType: string
	service_type: string
	ampereQuarter: string
	ampereSpendType: string
	ampereBusinessLine: string
}

export const vodFilter = () => {
	const project = getProject()

	const extraCardsByProviderOptions = $derived.by(() => {
		providers.push(
			...[
				{ value: 'Netflix', label: 'Netflix' },
				{ value: 'Amazon Prime Video', label: 'Amazon Prime' },
				{ value: 'Disney+', label: 'Disney +' },
				{ value: 'ITV', label: 'ITV' },
			],
		)
		return [{ value: null, label: 'No provider' }, ...providers.sort((a, b) => a.label.localeCompare(b.label))]
	})

	const ampereQuarterOptions = $derived.by(() => {
		if (!project.filter.iso) {
			return [{ value: null, label: '' }]
		}

		AsyncFilter({
			variables: {
				projectId: project.current?._id,
				filter: {
					ampereQuarter: {
						iso: project.filter.iso,
					},
				},
			},
		}).then(({ data }) => [...(data?.filter?.ampereQuarter || [])])
	})

	const ampereSpendTypeOptions = $derived.by(() => {
		if (!project.filter.iso) {
			return [{ value: null, label: '' }]
		}

		AsyncFilter({
			variables: {
				projectId: project.current?._id,
				filter: {
					ampereSpendType: {
						iso: project.filter.iso,
					},
				},
			},
		}).then(({ data }) => [{ value: null, label: 'All' }, ...(data?.filter?.ampereSpendType || [])])
	})

	const ampereBusinessLineOptions = $derived.by(() => {
		if (!project.filter.iso) {
			return [{ value: null, label: '' }]
		}

		AsyncFilter({
			variables: {
				projectId: project.current?._id,
				filter: {
					ampereBusinessLine: {
						iso: project.filter.iso,
					},
				},
			},
		}).then(({ data }) => [{ value: null, label: 'All' }, ...(data?.filter?.ampereBusinessLine || [])])
	})

	return [
		isoFilter(project),
		timeUnitFilter(),
		{
			key: 'ampereVodType',
			type: FieldType.SELECT,
			props: {
				label: 'VoD Type',
				placeholder: 'All',
				default: 'avod',
				options: [
					{ value: 'avod', label: 'AVoD' },
					{ value: 'svod', label: 'SVoD' },
				],
			},
		},
		{
			key: 'service_type',
			type: FieldType.SELECT,
			props: {
				label: 'Service Type',
				placeholder: 'All',
				description: '(for Consumer Monthly Users only)',
				default: null,
				options: [
					{ value: null, label: 'All' },
					{ value: 'AVoD', label: 'AVoD' },
					{ value: 'Catch-up', label: 'Catch-up' },
				],
			},
			resetOn: ['iso', 'ampereVodType'],
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.ampereVodType !== 'avod',
		},
		{
			key: 'extraCardsByProvider',
			type: FieldType.SELECT,
			props: {
				label: 'Providers',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: () => extraCardsByProviderOptions,
			},
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso?.length ||
				!['GB'].includes(filter.iso),
		},
		{
			key: 'extraCardsByProvider',
			type: FieldType.SELECT,
			props: {
				label: 'Providers',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: derived(filteredProviders, (providers) => {
					providers.push(
						...[
							{ value: 'Netflix', label: 'Netflix' },
							{ value: 'Amazon Prime Video', label: 'Amazon Prime' },
							{ value: 'Disney+', label: 'Disney +' },
							{ value: 'Mediaset Espana', label: 'Mediaset Espana' },
							{ value: 'Atresmedia', label: 'Atresmedia' },
						],
					)
					return [{ value: null, label: 'No provider' }, ...providers.sort((a, b) => a.label.localeCompare(b.label))]
				}),
			},
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso?.length ||
				!['ES'].includes(filter.iso),
		},
		{
			key: 'extraCardsByProvider',
			type: FieldType.SELECT,
			props: {
				label: 'Providers',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: derived(filteredProviders, (providers) => {
					providers.push(
						...[
							{ value: 'Netflix', label: 'Netflix' },
							{ value: 'Amazon Prime Video', label: 'Amazon Prime' },
							{ value: 'Disney+', label: 'Disney +' },
							{ value: 'Mediaset', label: 'Mediaset Italia' },
						],
					)
					return [{ value: null, label: 'No provider' }, ...providers.sort((a, b) => a.label.localeCompare(b.label))]
				}),
			},
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso?.length ||
				!['IT'].includes(filter.iso),
		},
		{
			key: 'extraCardsByProvider',
			type: FieldType.SELECT,
			props: {
				label: 'Providers',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: derived(filteredProviders, (providers) => {
					providers.push(
						...[
							{ value: 'Netflix', label: 'Netflix' },
							{ value: 'Amazon Prime Video', label: 'Amazon Prime' },
							{ value: 'Disney+', label: 'Disney +' },
							{ value: 'ProSiebenSat1', label: 'ProSieben' },
							{ value: 'RTL', label: 'RTL' },
						],
					)
					return [{ value: null, label: 'No provider' }, ...providers.sort((a, b) => a.label.localeCompare(b.label))]
				}),
			},
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso?.length ||
				!['DE'].includes(filter.iso),
		},
		{
			key: 'extraCardsByProvider',
			type: FieldType.SELECT,
			props: {
				label: 'Providers',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: derived(filteredProviders, (providers) => {
					return [{ value: null, label: 'No provider' }, ...providers]
				}),
			},
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso?.length ||
				['GB', 'ES', 'IT', 'DE'].includes(filter.iso),
		},
		{
			key: 'ampereQuarter',
			type: FieldType.SELECT,
			props: {
				label: 'Quarter',
				placeholder: 'All',
				// multiple: true,
				default: format(new Date(), 'QQQ yyyy'),
				options: () => ampereQuarterOptions,
			},
			resetOn: ['ampereVodType'],
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso ||
				!['GB', 'ES', 'IT', 'DE'].includes(filter.iso) ||
				filter.ampereVodType !== 'svod',
		},
		{
			key: 'ampereSpendType',
			type: FieldType.SELECT,
			props: {
				label: 'Spend Type',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: () => ampereSpendTypeOptions,
			},
			resetOn: ['ampereVodType'],
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso ||
				!['GB', 'ES', 'IT', 'DE'].includes(filter.iso) ||
				filter.ampereVodType !== 'svod',
		},
		{
			key: 'ampereBusinessLine',
			type: FieldType.SELECT,
			props: {
				label: 'Business Line',
				placeholder: 'All',
				// multiple: true,
				default: null,
				options: () => ampereBusinessLineOptions,
			},
			resetOn: ['ampereVodType'],
			hide: (filter: VodFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso ||
				!['GB', 'ES', 'IT', 'DE'].includes(filter.iso) ||
				filter.ampereVodType !== 'svod',
		},
	]
}
