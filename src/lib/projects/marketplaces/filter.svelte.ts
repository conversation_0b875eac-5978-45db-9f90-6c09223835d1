import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, extraCardsFilter, timeUnitFilter, verticalTypeFilter } from '../common.filters.svelte'

import type {
	Iso3166Alpha2,
	TimeUnit,
	MarketplacesStockType,
	DealerType,
	ProductType,
} from '$lib/graphql/generated/gateway'

export interface MarketplacesFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	verticalType: string
	extraCardsByProvider: string
	stockType: MarketplacesStockType
	condition: string
	dealerType: DealerType
	isPaying: boolean
	listingCountRange: string
	state: string
	dataPresentation: string
	productType: ProductType
	petsType?: string
	businessType?: string
}

export const marketplacesFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		verticalTypeFilter(project),
		extraCardsFilter(project),
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Listing Type',
				placeholder: 'All',
				description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'leisure', label: 'Leisure' },
					{ value: 'fashion', label: 'Fashion' },
					{ value: 'kids', label: 'Kids' },
					{ value: 'electronics', label: 'Electronics' },
					{ value: 'house_appliances', label: 'House Appliances' },
					{ value: 'car_parts', label: 'Car Parts' },
					{ value: 'services', label: 'Services' },
					{ value: 'other', label: 'Others' },
				],
			},
			hide: (filter: MarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso === 'BR',
		},
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Listing Type',
				placeholder: 'All',
				description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'leisure', label: 'Leisure' },
					{ value: 'fashion', label: 'Fashion' },
					{ value: 'women', label: 'Women' },
					{ value: 'men', label: 'Men' },
					{ value: 'kids', label: 'Kids' },
					{ value: 'electronics', label: 'Electronics' },
					{ value: 'house_appliances', label: 'House Appliances' },
					{ value: 'car_parts', label: 'Car Parts' },
					{ value: 'other', label: 'Others' },
				],
			},
			hide: (filter: MarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso !== 'BR',
		},
		{
			key: 'condition',
			type: FieldType.SELECT,
			props: {
				label: 'Listing Condition',
				placeholder: 'All',
				description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'new', label: 'New' },
					{ value: 'used', label: 'Used' },
				],
			},
			hide: (filter: MarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso !== 'BR',
		},
		{
			key: 'dealerType',
			type: FieldType.SELECT,
			props: {
				label: 'Seller Type',
				placeholder: 'All',
				description: 'Listings, Sellers',
				options: [
					{ value: null, label: 'All' },
					{ value: 'private', label: 'Private' },
					{ value: 'dealer', label: 'Professional' },
				],
			},
		},
		{
			key: 'isPaying',
			type: FieldType.SELECT,
			props: {
				label: 'Account Type',
				placeholder: 'Total',
				description: 'Sellers',
				options: [
					{ value: null, label: 'All' },
					{ value: true, label: 'Paying' },
				],
			},
		},
		{
			key: 'listingCountRange',
			type: FieldType.SELECT,
			props: {
				label: 'Listing Count',
				placeholder: 'All',
				description: 'Sellers',
				options: [
					{ value: null, label: 'All' },
					{ value: '0-5', label: '0-5' },
					{ value: '6-10', label: '6-10' },
					{ value: '11-20', label: '11-20' },
					{ value: '21-50', label: '21-50' },
					{ value: '51-100', label: '51-100' },
					{ value: '101-200', label: '101-200' },
					{ value: '+200', label: '+200' },
				],
			},
			resetOn: ['iso'],
			hide: (filter: MarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso !== 'BR',
		},
		{
			key: 'state',
			type: FieldType.SELECT,
			props: {
				label: 'State',
				placeholder: 'All',
				description: 'Sellers',
				options: [
					{ value: null, label: 'All' },
					{ value: 'BR-AC', label: 'Acre' },
					{ value: 'BR-AL', label: 'Alagoas' },
					{ value: 'BR-AP', label: 'Amapá' },
					{ value: 'BR-AM', label: 'Amazonas' },
					{ value: 'BR-BA', label: 'Bahia' },
					{ value: 'BR-CE', label: 'Ceará' },
					{ value: 'BR-DF', label: 'Distrito Federal' },
					{ value: 'BR-ES', label: 'Espírito Santo' },
					{ value: 'BR-GO', label: 'Goiás' },
					{ value: 'BR-MA', label: 'Maranhão' },
					{ value: 'BR-MT', label: 'Mato Grosso' },
					{ value: 'BR-MS', label: 'Mato Grosso do Sul' },
					{ value: 'BR-MG', label: 'Minas Gerais' },
					{ value: 'BR-PA', label: 'Pará' },
					{ value: 'BR-PB', label: 'Paraíba' },
					{ value: 'BR-PR', label: 'Paraná' },
					{ value: 'BR-PE', label: 'Pernambuco' },
					{ value: 'BR-PI', label: 'Piauí' },
					{ value: 'BR-RJ', label: 'Rio de Janeiro' },
					{ value: 'BR-RN', label: 'Rio Grande do Norte' },
					{ value: 'BR-RS', label: 'Rio Grande do Sul' },
					{ value: 'BR-RO', label: 'Rondônia' },
					{ value: 'BR-RR', label: 'Roraima' },
					{ value: 'BR-SC', label: 'Santa Catarina' },
					{ value: 'BR-SP', label: 'São Paulo' },
					{ value: 'BR-SE', label: 'Sergipe' },
					{ value: 'BR-TO', label: 'Tocantins' },
				],
			},
			resetOn: ['iso'],
			hide: (filter: MarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso !== 'BR',
		},
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: (filter: MarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !['DE', 'CH'].includes(filter.iso || ''),
		},
	]
}

// const test = filter.filter((item) => item.type === FieldType.SELECT).map((field) => {
// 	return {
// 		test: field.props.options
// 	}
// })

// const test2 = filter[0].props.options
