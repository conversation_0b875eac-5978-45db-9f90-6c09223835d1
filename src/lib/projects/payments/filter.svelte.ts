import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { FieldType } from '$lib/modules/Form/form.interface'

import {
	isoFilter,
	providersFilter,
	extraCardsFilter,
	timeUnitFilter,
	verticalTypeFilter,
} from '../common.filters.svelte'

import type { TimeUnit, Iso3166Alpha2 } from '$lib/graphql/generated/gateway'

export interface PaymentsFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	extraCardsByProvider: string
	verticalType: string
	providers: string[]
	month: string
	tableToShow: string
	webpage: string
}

export const paymentsFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		verticalTypeFilter(project),
		providersFilter(project),
		// monthFilter(),
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				label: 'Show',
				default: 'all',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		{
			key: 'webpage',
			type: FieldType.SELECT,
			props: {
				label: 'Webpage',
				placeholder: 'Total',
				default: 'main',
				options: [
					{ value: 'main', label: 'Main Page' },
					{ value: 'login', label: 'Login Page' },
				],
			},
			hide: () => page.route?.id === ('project/[projectId]/table' as RouteId),
		},
		extraCardsFilter(project),
	]
}
