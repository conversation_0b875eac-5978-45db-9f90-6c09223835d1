import { startOfISOWeek, subWeeks, format } from 'date-fns'

import { page } from '$app/state'

import { formatData, type ProjectState } from '$lib/components/Project'
import { TimeUnit, AsyncAnalysis, type SegmentationResult } from '$lib/graphql/generated/gateway'
import { CardType, ChartType, type DashboardCard } from '$lib/modules/Dashboard/dashboard.interface'

import { hasSelectionForIso, resolvePiePositions, filterSelectionForIso, sumArraysPerObjKey } from './utils'

import type { RealestateMarketplacesFilterProperties } from '../filter.svelte'

function buildAgentSegmentationCards(
	data: SegmentationResult,
	filter: RealestateMarketplacesFilterProperties,
	iso: string,
	isCompany: boolean,
) {
	const dataset = []
	const dataset2 = []
	if (!isCompany) {
		dataset.push({
			name: 'Overall',
			data: sumArraysPerObjKey(
				data.series.map((entry) => entry.data),
				'dealerType',
			),
		})
		dataset2.push({
			name: 'Overall',
			data: sumArraysPerObjKey(
				data.series.map((entry) => entry.data),
				'agentType',
			),
		})

		if (filter.enterpriseAggregate?.length && hasSelectionForIso(filter.enterpriseAggregate, iso)) {
			dataset.push({
				name: 'Selection',
				data: sumArraysPerObjKey(
					data.series
						.filter((entry) => filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
					'dealerType',
				),
			})
			dataset2.push({
				name: 'Selection',
				data: sumArraysPerObjKey(
					data.series
						.filter((entry) => filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
					'agentType',
				),
			})
			dataset.push({
				name: 'Other',
				data: sumArraysPerObjKey(
					data.series
						.filter((entry) => !filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
					'dealerType',
				),
			})
			dataset2.push({
				name: 'Other',
				data: sumArraysPerObjKey(
					data.series
						.filter((entry) => !filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
					'agentType',
				),
			})
		}
	} else {
		const enterprises = filterSelectionForIso(filter.enterprise, iso)
		dataset.push(
			...enterprises.map((enterprise) => ({
				name: enterprise.label,
				data: sumArraysPerObjKey(
					data.series.filter((entry) => entry.name.toLowerCase() === enterprise.value).map((entry) => entry.data),
					'dealerType',
				),
			})),
		)
		dataset2.push(
			...enterprises.map((enterprise) => ({
				name: enterprise.label,
				data: sumArraysPerObjKey(
					data.series.filter((entry) => entry.name.toLowerCase() === enterprise.value).map((entry) => entry.data),
					'agentType',
				),
			})),
		)
	}
	const positions = resolvePiePositions(dataset.length, 4, true)
	return {
		title: dataset.map((entry, i) => {
			return {
				subtext: entry.name,
				left: positions[i].title[0],
				top: positions[i].title[1],
				textAlign: 'center',
				textVerticalAlign: 'middle',
			}
		}),
		series: [
			...dataset.map((entry, i) => {
				return {
					data: entry.data,
					type: ChartType.PIE,
					name: entry.name,
					clockwise: true,
					center: positions[i].chart,
					label: {
						show: false,
						position: 'outside' as const,
					},
					radius: positions[i].radius,
					iso,
				}
			}),
			...dataset2.map((entry, i) => {
				return {
					data: entry.data,
					type: ChartType.PIE,
					name: entry.name,
					clockwise: true,
					center: positions[i].chart,
					label: {
						show: false,
						position: 'outside' as const,
					},
					radius: positions[i].innerRadius,
					iso,
				}
			}),
		],
	}
}

export function agentSegmentation(project: ProjectState) {
	// Não tem lógica ter o businessType nos agents
	// if (!project.filter.businessTypes) {
	//   return []
	// }

	const cards: DashboardCard[] = []
	const timeUnit = TimeUnit.Weeks
	// Só queremos o resultado da semana anterior
	const snapTime = format(subWeeks(startOfISOWeek(new Date()), 1), timeUnit === TimeUnit.Weeks ? 'GGGG/WW' : 'yyyy/MM')

	if (project.filter.analysis && !!project.filter.isos?.length) {
		for (const iso of project.filter.isos) {
			cards.push(
				project.generateCard({
					type: CardType.CHART,
					group: 'pie',
					chartType: ChartType.PIE,
					header: {
						title: 'Market Share Analysis',
						prefix: iso,
					},
					class: 'col-100 row-4',
					options: {
						series: 'years',
					},
					analysis: {
						type: 'realEstateAgentSegmentation',
						filter: {
							chartType: 'pie',
							timeUnit,
							iso,
							dataType: 'dealers',
							snapTime,
							// propertyScope: filter.propertyScope,
						},
						formatData: (filter: RealestateMarketplacesFilterProperties, data) => {
							return formatData(project, data ? buildAgentSegmentationCards(data, filter, iso, false) : undefined)
						},
					},
					hide: '!filter.isos',
				}),
			)

			if (
				project.filter.enterprise?.length &&
				hasSelectionForIso(project.enterprises, project.filter.enterprise, iso)
			) {
				cards.push(
					project.generateCard({
						type: CardType.CHART,
						group: 'pie',
						chartType: ChartType.PIE,
						header: {
							title: 'Market Share Analysis Per Company',
							prefix: iso,
						},
						class: 'col-100 row-4',
						options: {
							series: 'years',
						},
						isLoading: marketShareAnalysisPerCountryIsLoading,
						data: derived(projectStatus, (_, set) => {
							if (!filter || !filter.isos) return

							marketShareAnalysisPerCountryIsLoading.set(true)

							AsyncAnalysis({
								variables: {
									projectId: page.params?.projectId,
									analysis: {
										realEstateAgentSegmentation: {
											chartType: 'pie',
											timeUnit,
											iso,
											dataType: 'dealers',
											snapTime,
											// propertyScope: filter.propertyScope,
										},
									},
								},
							})
								.then(({ data }) => {
									set(
										formatData(
											data?.analysis.realEstateAgentSegmentation
												? buildAgentSegmentationCards(data?.analysis.realEstateAgentSegmentation, filter, iso, false)
												: undefined,
										),
									)
								})
								.finally(() => marketShareAnalysisPerCountryIsLoading.set(false))
						}),
						hide: '!filter.isos',
					}),
				)
			}
		}
	}

	return cards
}
