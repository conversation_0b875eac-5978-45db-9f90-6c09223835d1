import { derived, writable } from 'svelte/store'

import { page } from '$app/stores'

import { TimeUnit, AsyncAnalysis, Iso3166Alpha2, type SeriesItem } from '$lib/graphql/generated/gateway'
import { CardType, ChartType, type DashboardCard } from '$lib/modules/Dashboard/dashboard.interface'

import { average_of_arrays, getRoundDecimals } from './utils'

import type { RealestateMarketplacesFilterProperties } from '../filter.svelte'

import { formatData, projectStatus } from '$lib/components/Project/project.service'

const kpiQueries: {
	[key: string]: (iso: Iso3166Alpha2, businessType: string) => unknown
} = {
	'Marketplace assets value': (iso: Iso3166Alpha2, businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'inventory',
		chartName: 'gmvCurrent',
		// dealerType: filter.dealerType,
		// stockType: filter.stockType,
		// verticalType: filter.verticalType,
		businessType,
		// propertyScope: filter.propertyScope,
	}),
	'Number of listings': (iso: Iso3166Alpha2, businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'inventory',
		// stockType: filter.stockType,
		// dealerType: filter.dealerType,
		// productType: filter.productType,
		// state: filter.state,
		businessType,
		// propertyScope: filter.propertyScope,
	}),
	'Average price': (iso: Iso3166Alpha2, businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'inventory',
		chartName: 'avgPrice',
		hasPrice: true,
		// dealerType: filter.dealerType,
		// stockType: filter.stockType,
		// verticalType: filter.verticalType,
		businessType,
		// propertyScope: filter.propertyScope,
	}),
	'Average square meters': (iso: Iso3166Alpha2, businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'inventory',
		chartName: 'avgSqm',
		hasArea: true,
		// dealerType: filter.dealerType,
		// stockType: filter.stockType,
		// verticalType: filter.verticalType,
		businessType,
		// propertyScope: filter.propertyScope,
	}),
	'Average price per square meter': (iso: Iso3166Alpha2, businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'inventory',
		chartName: 'avgPriceSqm',
		hasPrice: true,
		hasArea: true,
		// dealerType: filter.dealerType,
		// stockType: filter.stockType,
		// verticalType: filter.verticalType,
		businessType,
		// propertyScope: filter.propertyScope,
	}),
	'Number of agents': (iso: Iso3166Alpha2, _businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'dealers',
		// isPaying: filter.isPaying,
		// dealerType: filter.dealerType,
		// stockTypes: filter.stockTypes ? stockTypesFilterMap[filter.stockTypes] : undefined,
	}),
	'New listings': (iso: Iso3166Alpha2, businessType: string) => ({
		timeUnit: TimeUnit.Weeks,
		iso,
		dataType: 'inventory',
		isNew: true,
		// stockType: filter.stockType,
		// dealerType: filter.dealerType,
		// productType: filter.productType,
		// state: filter.state,
		businessType,
		// propertyScope: filter.propertyScope,
	}),
}

export function kpiEvolution(filter: RealestateMarketplacesFilterProperties) {
	if (!filter.businessTypes) {
		return []
	}

	const cards: DashboardCard[] = []

	if (filter.analysis && filter.isos?.length && filter.kpis?.length && filter.businessTypes?.length) {
		for (const iso of filter.isos) {
			for (const kpi of filter.kpis) {
				for (const businessType of filter.businessTypes) {
					const roundDecimals = getRoundDecimals(kpi)

					const isLoading = writable(true)

					cards.push({
						type: CardType.CHART,
						group: 'line',
						chartType: ChartType.LINE,
						header: {
							title: kpi,
							description: businessType,
							prefix: iso,
						},
						class: `${filter.kpis?.length <= 1 ? 'col-100' : 'col-50'} row-4`,
						options: {
							series: TimeUnit.Weeks,
							...((roundDecimals || roundDecimals === 0) && { roundDecimals }),
						},
						isLoading,
						data: derived(projectStatus, (_, set) => {
							if (!filter || !filter.isos) return

							isLoading.set(true)

							AsyncAnalysis({
								variables: {
									projectId: page.params?.projectId,
									analysis: {
										realEstateStats: kpiQueries[kpi](iso, businessType),
									},
								},
							})
								.then(({ data }) => data?.analysis.realEstateStats)
								.then((data) => {
									if (!data) return

									const result = { ...data }
									result.series = data?.series.filter(
										(entry) =>
											entry.name === 'Average' ||
											(filter.enterprise?.length && filter.enterprise.indexOf(entry.name.toLowerCase()) !== -1),
									)
									if (!result?.series) result.series = []
									else {
										result.series.splice(0, 0, {
											name: 'Average',
											type: 'line' as const,
											iso,
											symbol: 'triangle',
											data: average_of_arrays(data.series?.map((entry) => entry.data)),
										} as SeriesItem & { symbol: string })

										if (filter.enterpriseAggregate?.length) {
											result.series.splice(1, 0, {
												name: 'Aggregate Selection',
												type: 'line' as const,
												iso,
												symbol: 'triangle',
												data: average_of_arrays(
													data.series
														.filter((entry) => filter.enterpriseAggregate?.indexOf(entry.name.toLowerCase()) !== -1)
														.map((entry) => entry.data),
												),
											} as SeriesItem & { symbol: string })

											result.series.splice(2, 0, {
												name: 'Aggregate Others',
												type: 'line' as const,
												iso,
												symbol: 'triangle',
												data: average_of_arrays(
													data.series
														.filter((entry) => filter.enterpriseAggregate?.indexOf(entry.name.toLowerCase()) === -1)
														.map((entry) => entry.data),
												),
											} as SeriesItem & { symbol: string })
										}
									}

									return result
								})
								.then((data) => set(formatData(data)))
								.finally(() => isLoading.set(false))
						}),
						hide: '!filter.isos',
					})
				}
			}
		}
	}

	return cards
}
