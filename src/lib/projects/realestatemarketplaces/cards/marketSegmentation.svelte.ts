import { format, subWeeks, startOfISOWeek } from 'date-fns'
import { derived, writable } from 'svelte/store'

import { page } from '$app/stores'

import { type SegmentationResult, TimeUnit, AsyncAnalysis } from '$lib/graphql/generated/gateway'
import { CardType, ChartType, type DashboardCard } from '$lib/modules/Dashboard/dashboard.interface'

import { filterSelectionForIso, hasSelectionForIso, resolvePiePositions, sumArraysPerObjKey } from './utils'

import type { RealestateMarketplacesFilterProperties } from '../filter.svelte'

import { formatData, projectStatus } from '$lib/components/Project/project.service'

function buildMarketSegmentationCards(
	data: SegmentationResult,
	filter: RealestateMarketplacesFilterProperties,
	iso: string,
	isCompany: boolean,
) {
	const dataset = []
	if (!isCompany) {
		dataset.push({
			name: 'Overall',
			data: sumArraysPerObjKey(
				data.series.map((entry) => entry.data),
				'name',
			),
		})

		if (filter.enterpriseAggregate?.length && hasSelectionForIso(filter.enterpriseAggregate, iso)) {
			dataset.push({
				name: 'Selection',
				data: sumArraysPerObjKey(
					data.series
						.filter((entry) => filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
					'name',
				),
			})

			dataset.push({
				name: 'Other',
				data: sumArraysPerObjKey(
					data.series
						.filter((entry) => !filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
					'name',
				),
			})
		}
	} else {
		const enterprises = filterSelectionForIso(filter.enterprise, iso)
		dataset.push(
			...enterprises.map((enterprise) => ({
				name: enterprise.label,
				data: sumArraysPerObjKey(
					data.series.filter((s) => s.name.toLowerCase() === enterprise.value).map((entry) => entry.data),
					'name',
				),
			})),
		)
	}

	const positions = resolvePiePositions(dataset.length)
	return {
		title: dataset.map((entry, i) => {
			return {
				subtext: entry.name,
				left: positions[i].title[0],
				top: positions[i].title[1],
				textAlign: 'center',
				textVerticalAlign: 'middle',
			}
		}),
		series: dataset.map((entry, i) => {
			return {
				data: entry.data,
				type: ChartType.PIE,
				name: entry.name,
				clockwise: true,
				center: positions[i].chart,
				label: {
					show: false,
					position: 'outside' as const,
				},
				radius: positions[i].radius,
				iso,
			}
		}),
	}
}

export function marketSegmentation(filter: RealestateMarketplacesFilterProperties) {
	if (!filter.businessTypes) {
		return []
	}

	const cards: DashboardCard[] = []
	const timeUnit = TimeUnit.Weeks
	// Só queremos o resultado da semana anterior
	const snapTime = format(subWeeks(startOfISOWeek(new Date()), 1), timeUnit === TimeUnit.Weeks ? 'GGGG/WW' : 'yyyy/MM')

	if (filter.analysis && !!filter.isos?.length) {
		for (const iso of filter.isos) {
			for (const businessType of filter.businessTypes) {
				const marketShareAnalysisIsLoading = writable(true)
				const marketShareAnalysisPerCountryIsLoading = writable(true)

				cards.push({
					type: CardType.CHART,
					group: 'pie',
					chartType: ChartType.PIE,
					header: {
						title: 'Market Share Analysis',
						description: businessType,
						prefix: iso,
					},
					class: 'col-100 row-4',
					options: {
						series: 'years',
					},
					isLoading: marketShareAnalysisIsLoading,
					data: derived(projectStatus, (_, set) => {
						if (!filter || !filter.isos) return

						marketShareAnalysisIsLoading.set(true)

						AsyncAnalysis({
							variables: {
								projectId: page.params?.projectId,
								analysis: {
									realEstateMarketSegmentation: {
										chartType: 'pie',
										timeUnit,
										iso,
										dataType: 'inventory',
										snapTime,
										businessType,
										// propertyScope: filter.propertyScope,
									},
								},
							},
						})
							.then(({ data }) => {
								set(
									formatData(
										data?.analysis.realEstateAgentSegmentation
											? buildMarketSegmentationCards(data?.analysis.realEstateAgentSegmentation, filter, iso, true)
											: undefined,
									),
								)
							})
							.finally(() => marketShareAnalysisIsLoading.set(false))
					}),
					hide: '!filter.isos',
				})

				if (filter.enterprise?.length && hasSelectionForIso(filter.enterprise, iso)) {
					cards.push({
						type: CardType.CHART,
						group: 'pie',
						chartType: ChartType.PIE,
						header: {
							title: 'Market Share Analysis Per Company',
							description: businessType,
							prefix: iso,
						},
						class: 'col-100 row-4',
						options: {
							series: 'years',
						},
						isLoading: marketShareAnalysisPerCountryIsLoading,
						data: derived(projectStatus, (_, set) => {
							if (!filter || !filter.isos) return

							marketShareAnalysisPerCountryIsLoading.set(true)

							AsyncAnalysis({
								variables: {
									projectId: page.params?.projectId,
									analysis: {
										realEstateMarketSegmentation: {
											chartType: 'pie',
											timeUnit,
											iso,
											dataType: 'inventory',
											snapTime,
											businessType,
											// propertyScope: filter.propertyScope,
										},
									},
								},
							})
								.then(({ data }) => {
									set(
										formatData(
											data?.analysis.realEstateAgentSegmentation
												? buildMarketSegmentationCards(data?.analysis.realEstateAgentSegmentation, filter, iso, true)
												: undefined,
										),
									)
								})
								.finally(() => marketShareAnalysisPerCountryIsLoading.set(false))
						}),
						hide: '!filter.isos',
					})
				}
			}
		}
	}
	return cards
}
