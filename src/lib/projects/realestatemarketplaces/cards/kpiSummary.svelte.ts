import { startOfISOWeek, format, subWeeks } from 'date-fns'
import { derived, writable } from 'svelte/store'

import { page } from '$app/stores'

import { TimeUnit, AsyncAnalysis } from '$lib/graphql/generated/gateway'
import { CardType, ChartType, type DashboardCard } from '$lib/modules/Dashboard/dashboard.interface'

import type { RealestateMarketplacesFilterProperties } from '../filter.svelte'

import { formatData, projectStatus } from '$lib/components/Project/project.service'

export function kpiSummary(filter: RealestateMarketplacesFilterProperties) {
	if (!filter.businessTypes) {
		return []
	}

	const cards: DashboardCard[] = []

	if (filter.analysis && !!filter.isos?.length) {
		const currTimeMoment = startOfISOWeek(new Date())
		const start = format(subWeeks(currTimeMoment, 2), 'yyyy/II')
		const end = format(subWeeks(currTimeMoment, 1), 'yyyy/II')

		for (const iso of filter.isos) {
			for (const businessType of filter.businessTypes) {
				const isLoading = writable(true)

				cards.push({
					type: CardType.CHART,
					group: 'bar',
					chartType: ChartType.BAR,
					header: {
						title: 'KPI Summary',
						description: businessType,
						prefix: iso,
					},
					class: 'col-100 row-4',
					options: {
						series: 'weeks',
						isPercent: true,
						hideAxis: false,
						// minY: -100,
						splitLine: true,
						roundDecimals: 2,
					},
					isLoading,
					data: derived(projectStatus, (_, set) => {
						if (!filter || !filter.isos) return

						isLoading.set(true)

						AsyncAnalysis({
							variables: {
								projectId: page.params?.projectId,
								analysis: {
									realEstateKPIVariation: {
										timeUnit: TimeUnit.Weeks,
										start,
										end,
										iso,
										businessType,
									},
								},
							},
						})
							.then(({ data }) => set(formatData(data?.analysis.realEstateKPIVariation)))
							.finally(() => isLoading.set(false))
					}),
					hide: '!filter.isos',
				})
			}
		}
	}

	return cards
}
