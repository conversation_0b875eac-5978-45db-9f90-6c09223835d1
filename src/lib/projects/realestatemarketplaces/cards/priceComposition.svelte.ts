import { format, subWeeks, startOfISOWeek } from 'date-fns'
import { derived, writable } from 'svelte/store'

import { page } from '$app/stores'

import {
	TimeUnit,
	AsyncAnalysis,
	Iso3166Alpha2,
	type SegmentationResult,
	type SegmentationSeries,
	type SegmentationSeriesItem,
} from '$lib/graphql/generated/gateway'
import { CardType, ChartType, type DashboardCard } from '$lib/modules/Dashboard/dashboard.interface'

import { sortObj, hasSelectionForIso } from './utils'

import type { Statistics } from '$lib/modules/Dashboard/toolbox.interface'
import type { RealestateMarketplacesFilterProperties } from '../filter.svelte'

import { formatData, projectStatus } from '$lib/components/Project/project.service'
import { statistics } from '$lib/modules/Dashboard/toolbox.service'

function sum_of_pricecomposition(arrays: SegmentationSeriesItem[][]) {
	let result: number[] = []
	for (const arr of arrays) {
		if (!arr || !arr.length) continue
		result = result.concat(arr.flatMap((item) => (item.value ? item.value : (item as unknown as number))))
	}
	return result
}

function buildPriceCompositionCards(
	data: SegmentationResult,
	filter: RealestateMarketplacesFilterProperties,
	tools: Statistics,
) {
	if (!data) return { series: [{ name: 'boxplot', type: 'boxplot', data: [], boxWidth: [7, 20] }] }

	const rawSeries =
		tools.boxPlotMaxFilter || tools.boxPlotMinFilter
			? data.series.map((series) => ({
					...series,
					data: series.data.filter((item) => {
						if (tools.boxPlotMinFilter && tools.boxPlotMaxFilter) {
							return item.value > tools.boxPlotMinFilter && item.value < tools.boxPlotMaxFilter
						} else if (tools.boxPlotMinFilter) {
							return item.value > tools.boxPlotMinFilter
						} else if (tools.boxPlotMaxFilter) {
							return item.value < tools.boxPlotMaxFilter
						}
					}),
				}))
			: data.series

	const dataset: Array<{
		name: string
		colorKey: string
		value: number[]
	}> = []

	for (const iso of filter.isos) {
		const seriesPerIso = sortObj(
			rawSeries.reduce(
				(acc, val) => {
					if (!(val.iso in acc)) {
						acc[val.iso] = []
					}
					acc[val.iso].push(val)
					return acc
				},
				{} as { [key in Iso3166Alpha2]: SegmentationSeries[] },
			),
		)

		if (!seriesPerIso[iso]?.length) continue

		const prices = sum_of_pricecomposition(
			seriesPerIso[iso].filter((entry) => entry.iso === iso).map((entry) => entry.data),
		)

		dataset.unshift({
			name: `Overall ${iso}`,
			colorKey: iso,
			value: prices,
		})

		if (filter.enterpriseAggregate?.length) {
			if (hasSelectionForIso(filter.enterpriseAggregate, iso)) {
				const pricesAggregate = sum_of_pricecomposition(
					seriesPerIso[iso]
						.filter((entry) => filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
				)

				dataset.unshift({
					name: `Selection ${iso}`,
					colorKey: `Aggregate Selection`,
					value: pricesAggregate,
				})

				const pricesOthers = sum_of_pricecomposition(
					seriesPerIso[iso]
						.filter((entry) => !filter.enterpriseAggregate.includes(entry.name.toLowerCase()))
						.map((entry) => entry.data),
				)

				dataset.unshift({
					name: `Others ${iso}`,
					colorKey: 'Aggregate Others',
					value: pricesOthers,
				})
			}
		}

		for (const enterprise of filter.enterprise) {
			if (hasSelectionForIso([enterprise], iso)) {
				const prices = sum_of_pricecomposition(
					seriesPerIso[iso].filter((entry) => entry.name.toLowerCase() === enterprise).map((entry) => entry.data),
				)

				dataset.unshift({
					name: enterprise,
					colorKey: enterprise,
					value: prices,
				})
			}
		}
	}

	const result = {
		dataset: [
			{
				source: dataset.map((entry) => entry.value),
			},
			{
				transform: {
					type: 'boxplot',
				},
			},
			{
				fromDatasetIndex: 1,
				fromTransformResult: 1,
			},
		],
		series: [
			{
				name: 'boxplot',
				type: ChartType.BOXPLOT,
				// datasetIndex: 1,
				data: dataset,
				boxWidth: [10, 20],
			},

			...(tools.boxPlotShowOutliers
				? [
						{
							name: 'outlier',
							type: ChartType.SCATTER,
							encode: { x: 1, y: 0 },
							datasetIndex: 2,
							// data: dataset.outliers
						},
					]
				: []),
		],
	}
	return result
}

export function priceComposition(filter: RealestateMarketplacesFilterProperties) {
	if (!filter.businessTypes) {
		return []
	}

	const cards: DashboardCard[] = []
	const timeUnit = 'weeks'
	// Só queremos o resultado da semana anterior
	const snapTime = format(subWeeks(startOfISOWeek(new Date()), 1), timeUnit === TimeUnit.Weeks ? 'GGGG/WW' : 'yyyy/MM')
	if (
		filter.analysis &&
		filter.subAnalysis === 'propertyEvolution' &&
		filter.isos?.length &&
		filter.propertyTypes?.length &&
		filter.businessTypes?.length
	) {
		for (const propertyType of filter.propertyTypes) {
			for (const businessType of filter.businessTypes) {
				const isLoading = writable(true)

				cards.push({
					type: CardType.CHART,
					group: 'boxplot',
					chartType: ChartType.BOXPLOT,
					header: {
						title: propertyType,
						description: businessType,
					},
					class: 'col-100 row-4',
					options: {
						series: 'weeks',
						legendShow: false,
					},
					isLoading,
					data: derived(
						[
							derived(projectStatus, (_, set) => {
								if (!filter || !filter.isos) return

								isLoading.set(true)

								AsyncAnalysis({
									variables: {
										projectId: page.params?.projectId,
										analysis: {
											realEstatePriceComposition: {
												chartType: 'boxplot',
												timeUnit: TimeUnit.Weeks,
												iso: { $in: filter.isos },
												dataType: 'priceComposition',
												snapTime,
												propertyType,
												businessType,
											},
										},
									},
								})
									.then(({ data }) => set(data?.analysis.realEstatePriceComposition))
									.finally(() => isLoading.set(false))
							}),
							statistics,
						],
						([$data, $statistics]) => {
							return formatData(buildPriceCompositionCards($data, filter, $statistics))
						},
					),
					hide: '!filter.isos',
				})
			}
		}
	} else if (
		filter.analysis &&
		filter.subAnalysis === 'countryEvolution' &&
		filter.isos?.length &&
		filter.businessTypes?.length
	) {
		for (const iso of filter.isos) {
			for (const businessType of filter.businessTypes) {
				const isLoading = writable(true)

				cards.push({
					type: CardType.CHART,
					group: 'boxplot',
					chartType: ChartType.BOXPLOT,
					header: {
						description: businessType,
						prefix: iso,
					},
					class: 'col-100 row-4',
					options: {
						series: 'weeks',
						legendShow: false,
					},
					isLoading,
					data: derived(
						[
							derived(projectStatus, (_, set) => {
								if (!filter || !filter.isos) return

								isLoading.set(true)

								AsyncAnalysis({
									variables: {
										projectId: page.params?.projectId,
										analysis: {
											realEstatePriceComposition: {
												chartType: 'boxplot',
												timeUnit: TimeUnit.Weeks,
												iso,
												dataType: 'priceComposition',
												snapTime,
												// propertyType,
												businessType,
											},
										},
									},
								})
									.then(({ data }) => set(data?.analysis.realEstatePriceComposition))
									.finally(() => isLoading.set(false))
							}),
							statistics,
						],
						([$data, $statistics]) => {
							return formatData(buildPriceCompositionCards($data, filter, $statistics))
						},
					),
					hide: '!filter.isos',
				})
			}
		}
	}

	return cards
}
