import { derived, writable } from 'svelte/store'

import { page } from '$app/stores'

import { AsyncAnalysis, TimeUnit } from '$lib/graphql/generated/gateway'
import { CardType, ChartType, type DashboardCard } from '$lib/modules/Dashboard/dashboard.interface'

import { filterSelectionForIso, getRoundDecimals, hasSelectionForIso } from './utils'

import type { RealestateMarketplacesFilterProperties } from '../filter.svelte'

import { formatData, projectStatus } from '$lib/components/Project/project.service'

export function priceEvolution(filter: RealestateMarketplacesFilterProperties) {
	if (!filter.businessTypes) {
		return []
	}

	const cards: DashboardCard[] = []

	if (
		filter.analysis &&
		filter.subAnalysis === 'propertyEvolution' &&
		filter.isos?.length &&
		filter.businessTypes?.length
	) {
		for (const iso of filter.isos) {
			for (const businessType of filter.businessTypes) {
				const roundDecimals = getRoundDecimals(filter.subAnalysis)

				const priceEvolutionIsLoading = writable(true)
				const aggregateSelectionIsLoading = writable(true)
				const aggregateOthersIsLoading = writable(true)

				cards.push({
					type: CardType.CHART,
					group: 'line',
					chartType: ChartType.LINE,
					header: {
						title: 'Price Evolution',
						description: businessType,
						prefix: iso,
					},
					class: `${filter.kpis?.length <= 1 ? 'col-100' : 'col-50'} row-4`,
					options: {
						series: 'weeks',
						legendShow: false,
						...((roundDecimals || roundDecimals === 0) && { roundDecimals }),
					},
					isLoading: priceEvolutionIsLoading,
					data: derived(projectStatus, (_, set) => {
						if (!filter || !filter.isos) return

						priceEvolutionIsLoading.set(true)

						AsyncAnalysis({
							variables: {
								projectId: page.params?.projectId,
								analysis: {
									realEstatePriceEvolution: {
										timeUnit: TimeUnit.Weeks,
										iso,
										dataType: 'inventory',
										chartName: 'avgPrice',
										hasPrice: true,
										// dealerType: filter.dealerType,
										// stockType: filter.stockType,
										businessType,
										// propertyScope: filter.propertyScope,
										// subAnalysis: filter.subAnalysis,
										subAnalysis: filter.subAnalysis,
									},
								},
							},
						})
							.then(({ data }) => set(formatData(data?.analysis.realEstatePriceEvolution)))
							.finally(() => priceEvolutionIsLoading.set(false))
					}),
					hide: '!filter.isos',
				})

				if (filter.enterpriseAggregate?.length && hasSelectionForIso(filter.enterpriseAggregate, iso)) {
					cards.push({
						type: CardType.CHART,
						group: 'line',
						chartType: ChartType.LINE,
						header: {
							title: 'Aggregate Selection',
							description: businessType,
							prefix: iso,
						},
						class: `${filter.kpis?.length <= 1 ? 'col-100' : 'col-50'} row-4`,
						options: {
							series: 'weeks',
							legendShow: false,
							...((roundDecimals || roundDecimals === 0) && { roundDecimals }),
						},
						isLoading: aggregateSelectionIsLoading,
						data: derived(projectStatus, (_, set) => {
							if (!filter || !filter.isos) return

							aggregateSelectionIsLoading.set(true)

							AsyncAnalysis({
								variables: {
									projectId: page.params?.projectId,
									analysis: {
										realEstatePriceEvolution: {
											timeUnit: TimeUnit.Weeks,
											iso,
											dataType: 'inventory',
											chartName: 'avgPrice',
											hasPrice: true,
											// dealerType: filter.dealerType,
											// stockType: filter.stockType,
											businessType,
											provider: { $in: filter.enterpriseAggregate },
											// propertyScope: filter.propertyScope,
											subAnalysis: filter.subAnalysis,
										},
									},
								},
							})
								.then(({ data }) => set(formatData(data?.analysis.realEstatePriceEvolution)))
								.finally(() => aggregateSelectionIsLoading.set(false))
						}),
						hide: '!filter.isos',
					})

					cards.push({
						type: CardType.CHART,
						group: 'line',
						chartType: ChartType.LINE,
						header: {
							title: 'Aggregate Others',
							description: businessType,
							prefix: iso,
						},
						class: `${filter.kpis?.length <= 1 ? 'col-100' : 'col-50'} row-4`,
						options: {
							series: 'weeks',
							legendShow: false,
							...((roundDecimals || roundDecimals === 0) && { roundDecimals }),
						},
						isLoading: aggregateOthersIsLoading,
						data: derived(projectStatus, (_, set) => {
							if (!filter || !filter.isos) return

							aggregateOthersIsLoading.set(true)

							AsyncAnalysis({
								variables: {
									projectId: page.params?.projectId,
									analysis: {
										realEstatePriceEvolution: {
											timeUnit: TimeUnit.Weeks,
											iso,
											dataType: 'inventory',
											chartName: 'avgPrice',
											hasPrice: true,
											// dealerType: filter.dealerType,
											// stockType: filter.stockType,
											businessType,
											provider: { $nin: filter.enterpriseAggregate },
											// propertyScope: filter.propertyScope,
											subAnalysis: filter.subAnalysis,
										},
									},
								},
							})
								.then(({ data }) => set(formatData(data?.analysis.realEstatePriceEvolution)))
								.finally(() => aggregateOthersIsLoading.set(false))
						}),
						hide: '!filter.isos',
					})
				}

				if (filter.enterprise?.length && hasSelectionForIso(filter.enterprise, iso)) {
					for (const enterprise of filterSelectionForIso(filter.enterprise, iso)) {
						const entrepriseIsLoading = writable(true)

						cards.push({
							type: CardType.CHART,
							group: 'line',
							chartType: ChartType.LINE,
							header: {
								title: enterprise.label,
								description: businessType,
								prefix: iso,
							},
							class: `${filter.kpis?.length <= 1 ? 'col-100' : 'col-50'} row-4`,
							options: {
								series: 'weeks',
								legendShow: false,
								...((roundDecimals || roundDecimals === 0) && { roundDecimals }),
							},
							isLoading: entrepriseIsLoading,
							data: derived(projectStatus, (_, set) => {
								if (!filter || !filter.isos) return

								entrepriseIsLoading.set(true)

								AsyncAnalysis({
									variables: {
										projectId: page.params?.projectId,
										analysis: {
											realEstatePriceEvolution: {
												timeUnit: TimeUnit.Weeks,
												iso,
												dataType: 'inventory',
												chartName: 'avgPrice',
												hasPrice: true,
												// dealerType: filter.dealerType,
												// stockType: filter.stockType,
												// verticalType: filter.verticalType,
												businessType,
												provider: enterprise,
												// propertyScope: filter.propertyScope,
												subAnalysis: filter.subAnalysis,
											},
										},
									},
								})
									.then(({ data }) => set(formatData(data?.analysis.realEstatePriceEvolution)))
									.finally(() => entrepriseIsLoading.set(false))
							}),
							hide: '!filter.isos',
						})
					}
				}
			}
		}
	} else if (
		filter.analysis &&
		filter.subAnalysis === 'countryEvolution' &&
		filter.isos?.length &&
		filter.propertyTypes?.length &&
		filter.businessTypes?.length
	) {
		// for (const propertyType of filter.propertyTypes) {
		// 	for (const businessType of filter.businessTypes) {
		// 		const roundDecimals = getRoundDecimals(filter.subAnalysis)
		// 		const propertyTypeIsLoading = writable(true)
		// 		cards.push({
		// 			type: CardType.CHART,
		// 			group: 'line',
		// 			chartType: ChartType.LINE,
		// 			header: {
		// 				title: propertyType,
		// 				description: businessType,
		// 				prefix: iso,
		// 			},
		// 			class: `${filter.kpis?.length <= 1 ? 'col-100' : 'col-50'} row-4`,
		// 			options: {
		// 				series: 'weeks',
		// 				legendShow: true,
		// 				...((roundDecimals || roundDecimals === 0) && { roundDecimals })
		// 			},
		// 			isLoading: propertyTypeIsLoading,
		// 			data: derived(projectStatus, (_, set) => {
		// 				if (!filter || !filter.isos) return
		// 				propertyTypeIsLoading.set(true)
		// 				AsyncAnalysis({
		// 					variables: {
		// 						projectId: page.params?.projectId,
		// 						analysis: {
		// 							realEstatePriceEvolution: {
		// 								timeUnit: TimeUnit.Weeks,
		// 								iso: filter.isos ? { $in: filter.isos } : undefined,
		// 								dataType: 'inventory',
		// 								chartName: 'avgPrice',
		// 								hasPrice: true,
		// 								// dealerType: filter.dealerType,
		// 								// stockType: filter.stockType,
		// 								businessType,
		// 								propertyType,
		// 								// propertyScope: filter.propertyScope,
		// 								subAnalysis: filter.subAnalysis
		// 							}
		// 						}
		// 					}
		// 				})
		// 					.then(({ data }) => data?.analysis.realEstatePriceEvolution)
		// 					.then((stats) => {
		// 						let interval: string[]
		// 								for (const data of stats.countries) {
		// 									if (typeof interval == 'undefined') {
		// 										interval = data.interval
		// 									} else if (interval.length < data.interval.length) {
		// 										interval = data.interval
		// 									}
		// 								}
		// 								const result = []
		// 								let resultEnterprises = []
		// 								for (const data of stats.countries) {
		// 									let series = data.series
		// 									if (data.interval.length < interval.length) {
		// 										const filler = new Array(interval.length - data.interval.length).fill(null)
		// 										series = series.map((s: any) => ({ ...s, data: [...filler, ...s.data] }))
		// 									}
		// 									resultEnterprises = [
		// 										...resultEnterprises,
		// 										...series.filter(
		// 											(entry) =>
		// 												entry.name === 'Average' ||
		// 												(filter.enterprise?.length && filter.enterprise.indexOf(entry.name.toLowerCase()) !== -1)
		// 										)
		// 									]
		// 									result.push({
		// 										name: `${data.iso}`,
		// 										type: 'line',
		// 										iso: data.iso,
		// 										symbol: 'triangle',
		// 										data: average_of_arrays(series.map((entry) => entry.data))
		// 									})
		// 									if (
		// 										filter.enterpriseAggregate?.length &&
		// 										series.filter((entry) => filter.enterpriseAggregate?.indexOf(entry.name.toLowerCase()) !== -1)
		// 											.length
		// 									) {
		// 										result.push({
		// 											name: `${data.iso} Aggregate`,
		// 											type: 'line',
		// 											iso: data.iso,
		// 											symbol: 'triangle',
		// 											data: average_of_arrays(
		// 												series
		// 													.filter((entry) => filter.enterpriseAggregate?.indexOf(entry.name.toLowerCase()) !== -1)
		// 													.map((entry) => entry.data)
		// 											)
		// 										})
		// 										result.push({
		// 											name: `${data.iso} Others`,
		// 											type: 'line',
		// 											iso: data.iso,
		// 											symbol: 'triangle',
		// 											data: average_of_arrays(
		// 												series
		// 													.filter((entry) => filter.enterpriseAggregate?.indexOf(entry.name.toLowerCase()) === -1)
		// 													.map((entry) => entry.data)
		// 											)
		// 										})
		// 									}
		// 								}
		// 								return { interval, series: [...result, ...resultEnterprises] }
		// 					})
		// 					.finally(() => propertyTypeIsLoading.set(false))
		// 			}),
		// 			hide: '!filter.isos',
		// 		})
		// 	}
		// }
	}

	return cards
}
