import type { Iso3166Alpha2, SegmentationSeriesItem } from '$lib/graphql/generated/gateway'

export function sortObj<T>(obj: Record<string, T>) {
	return Object.keys(obj)
		.sort()
		.reduce(
			(acc, key) => {
				acc[key] = obj[key]
				return acc
			},
			{} as Record<string, T>,
		)
}

export function average_of_arrays(arrays: Array<Array<number | null>>) {
	const result = []
	for (const array_index in arrays[0]) {
		let total = 0
		let nNulls = 0
		for (const arr of arrays) {
			if (arr[array_index] !== null) {
				++nNulls
			}
			total += +(arr[array_index] as number)
		}
		result.push(Math.round(total / (arrays.length - nNulls)))
	}

	return result
}

export const resolvePiePositions = (total: number, perLine = 4, inner = false) => {
	const nLines = Math.ceil(total / perLine)
	const result: Array<{
		chart: [string, string]
		title: [string, string]
		radius: [string, string]
		innerRadius: [string, string]
	}> = []

	const radiusMultiplier = nLines === 1 ? 1 : 1.4 / nLines

	const lineSize = 100 / nLines

	for (let line = 0; line < nLines; line++) {
		const colsInLine = line === nLines - 1 ? total % perLine || perLine : perLine
		const colSize = 100 / colsInLine

		for (let col = 0; col < colsInLine; col++) {
			result.push({
				chart: [col * colSize + colSize / 2 + '%', line * lineSize + lineSize / 2 + 5 + '%'],
				title: [col * colSize + colSize / 2 - 0.3 + '%', line * lineSize + lineSize / 2 + 1.2 + '%'],
				radius: [(inner ? 42 : 30) * radiusMultiplier + '%', 50 * radiusMultiplier + '%'],
				innerRadius: [30 * radiusMultiplier + '%', 38 * radiusMultiplier + '%'],
			})
		}
	}

	return result
}

export function hasSelectionForIso(
	enterprises: {
		value: string
		iso: Iso3166Alpha2
		label: string
	}[],
	selection: string[],
	iso: string,
) {
	return enterprises.some((el) => el.iso === iso && selection.includes(el.value))
}

export function filterSelectionForIso(
	enterprises: {
		value: string
		iso: Iso3166Alpha2
		label: string
	}[],
	selection: string[],
	iso: string,
) {
	return enterprises.filter((el) => el.iso === iso && selection.includes(el.value))
}

export function getRoundDecimals(kpi: string): number | undefined {
	switch (kpi) {
		case 'Marketplace assets value':
		case 'Average price':
		case 'Average price per square meter':
		case 'propertyEvolution':
		case 'countryEvolution':
			return 2
		case 'Average square meters':
			return 0
		default:
			return undefined
	}
}

export function sumArraysPerObjKey(arrays: SegmentationSeriesItem[][], dimention: string) {
	const result = []
	const map: Map<string, number> = new Map()

	for (const arr of arrays) {
		for (const item of arr) {
			const key = item.dimentions.find((el) => el.name === dimention)?.value
			if (key) {
				if (map.has(key)) {
					map.set(key, (map.get(key) || 0) + item.value)
				} else {
					map.set(key, item.value)
				}
			}
		}
	}

	for (const entry of map.entries()) {
		result.push({ name: entry[0], value: entry[1] })
	}

	return result
}
