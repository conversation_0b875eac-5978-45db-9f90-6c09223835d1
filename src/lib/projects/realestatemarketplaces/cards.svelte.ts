import { getProject } from '$lib/components/Project'

import { agentSegmentation } from './cards/agentSegmentation.svelte'
import { dashboard } from './cards/dashboard.svelte'
import { kpiEvolution } from './cards/kpiEvolution.svelte'
import { kpiSummary } from './cards/kpiSummary.svelte'
import { marketSegmentation } from './cards/marketSegmentation.svelte'
import { priceComposition } from './cards/priceComposition.svelte'
import { priceEvolution } from './cards/priceEvolution.svelte'

export const kpiCardMap = {
	agentSegmentation,
	dashboard,
	kpiEvolution,
	kpiSummary,
	marketSegmentation,
	priceComposition,
	priceEvolution,
}

export const realestateMarketplacesCards = () => {
	const project = getProject()

	const cards = $derived.by(() => {
		if (!project.filter?.analysis || !project.filter?.isos?.length) return []

		return kpiCardMap[project.filter.analysis as keyof typeof kpiCardMap](project)
	})

	return {
		get cards() {
			return cards
		},
	}
}
