import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { PropertyTypesMap } from '$lib/components/Project'
import {
	type Iso3166Alpha2,
	RealEstatesBusinessType,
	RealEstatesPropertyType,
	AsyncProjectCountries,
} from '$lib/graphql/generated/gateway'
import { FieldType, type Option } from '$lib/modules/Form/form.interface'

import { realestateMarketplacesColors } from './colors'

import type { kpiCardMap } from './cards.svelte'

const analysisTypes: Array<Option<RealestateMarketplacesFilterProperties, 'analysis'>> = [
	{
		value: 'kpiEvolution',
		label: 'KPIs Evolution',
		title:
			'Evolution of KPIs per selected target of analysis – Selected Companies (client’s vs direct competitors) vs Aggregate target',
	},
	{
		value: 'priceEvolution',
		label: 'Price Evolution',
		title: 'Price evolution analysis',
	},
	{
		value: 'priceComposition',
		label: 'Price Composition',
		title: 'Price composition per Property type per selected target of analysis',
	},
	{
		value: 'marketSegmentation',
		label: 'Market Segmentation',
		title: 'Marketplace segmentation according to property type per selected target of analysis',
	},
	{
		value: 'agentSegmentation',
		label: 'Agent Segmentation',
		title: 'Agent segmentation analysis',
	},
	{
		value: 'kpiSummary',
		label: 'KPIs Summary',
		title: 'Summary of KPIs performance and its percentual variability',
	},
	{
		value: 'dashboard',
		label: 'Dashboard',
		title: 'Dashboards',
	},
]

const kpis = [
	'Marketplace assets value',
	'Number of listings',
	'Average price',
	'Average square meters',
	'Average price per square meter',
	'Number of agents',
	'New listings',
]

export interface RealestateMarketplacesFilterProperties {
	analysis: keyof typeof kpiCardMap
	subAnalysis: string
	kpis: typeof kpis
	propertyTypes: RealEstatesPropertyType[]
	isos: Iso3166Alpha2[]
	businessTypes: RealEstatesBusinessType[]
	enterpriseAggregate: string[]
	enterprise: string[]
	portfolio: string[]
	company: string[]
	dashboard: string
}

export const realestateMarketplacesFilter = () => {
	const project = getProject()

	const colors = realestateMarketplacesColors()

	const isosOptions = $derived.by(() => {
		if (!project.current) return []

		return AsyncProjectCountries({
			variables: {
				projectId: project.current?._id,
			},
		}).then(({ data }) => data?.projectCountries?.map((country) => ({ value: country.iso, label: country.name })))
	})

	const enterpriseOptions = $derived.by(async () => {
		const enterprises = await project.enterprises
		if (!enterprises || !project.filter?.isos) return []

		return enterprises.filter((el) => project.filter.isos.includes(el.iso))
	})

	return [
		{
			key: 'analysis',
			type: FieldType.SELECT,
			props: {
				label: 'Type of analyses',
				placeholder: 'All',
				required: true,
				options: analysisTypes,
			},
		},
		{
			key: 'kpis',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'KPIs selection',
				options: kpis.map((kpi) => ({ value: kpi, label: kpi })),
				required: true,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.analysis !== 'kpiEvolution',
		},
		{
			key: 'subAnalysis',
			type: FieldType.SELECT,
			props: {
				label: 'Sub Analysis',
				options: [
					{
						value: 'countryEvolution',
						label: 'Country and provider prices per property type',
					},
					{
						value: 'propertyEvolution',
						label: 'Property type prices per country and provider',
					},
				],
				required: true,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.analysis !== 'priceEvolution',
		},
		{
			key: 'subAnalysis',
			type: FieldType.SELECT,
			props: {
				label: 'Sub Analysis',
				options: [
					{
						value: 'countryEvolution',
						label: 'Per country',
					},
					{
						value: 'propertyEvolution',
						label: 'Per property type',
					},
				],
				required: true,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.analysis !== 'priceComposition',
		},
		{
			key: 'dashboard',
			type: FieldType.SELECT,
			props: {
				label: 'Dashboard',
				options: [
					{
						value: 'company',
						label: 'Company',
					},
					{
						value: 'country',
						label: 'Country',
					},
					{
						value: 'portfolio',
						label: 'Portfolio',
					},
				],
				required: true,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.analysis !== 'dashboard',
		},
		{
			key: 'propertyTypes',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'Property Types',
				options: Object.keys(PropertyTypesMap)
					.sort((a, b) => a.localeCompare(b))
					.map((option) => ({
						value: option as RealEstatesPropertyType,
						label: PropertyTypesMap[option as keyof typeof PropertyTypesMap],
					})),
				required: true,
				// colors: project.colors
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!(
					(filter.analysis === 'priceComposition' && filter.subAnalysis === 'propertyEvolution') ||
					(filter.analysis === 'priceEvolution' && filter.subAnalysis === 'countryEvolution')
				),
		},
		{
			key: 'isos',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'Countries',
				placeholder: 'All',
				required: true,
				options: () => isosOptions,
				colors,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				(filter.analysis === 'dashboard' && filter.dashboard !== 'country'),
		},
		{
			key: 'businessTypes',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'Business Types',
				placeholder: 'All',
				required: true,
				options: [
					{
						value: 'sale',
						label: 'Sale',
					},
					{
						value: 'rent',
						label: 'Rent',
					},
				],
				colors,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				filter.analysis === 'dashboard' ||
				filter.analysis === 'agentSegmentation',
		},
		{
			key: 'enterpriseAggregate',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'Enterprise Aggregate',
				placeholder: 'Select',
				required: false,
				options: () => enterpriseOptions,
				colors,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				filter.analysis === 'kpiSummary' ||
				filter.analysis === 'dashboard' ||
				!filter.isos?.length,
		},
		{
			key: 'enterprise',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'Enterprises',
				placeholder: 'Select',
				required: false,
				options: () => enterpriseOptions,
				colors,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				filter.analysis === 'kpiSummary' ||
				filter.analysis === 'dashboard' ||
				!filter.isos?.length,
		},
		{
			key: 'portfolio',
			type: FieldType.SELECT,
			props: {
				multiple: true,
				label: 'Enterprise Aggregate',
				placeholder: 'Select',
				required: true,
				options: () => project.projectEnterprises,
				colors,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				filter.analysis !== 'dashboard' ||
				(filter.analysis === 'dashboard' && filter.dashboard !== 'portfolio'),
		},
		{
			key: 'company',
			type: FieldType.SELECT,
			props: {
				label: 'Enterprises',
				placeholder: 'Select',
				required: true,
				options: () => project.projectEnterprises,
			},
			hide: (filter: RealestateMarketplacesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				filter.analysis !== 'dashboard' ||
				(filter.analysis === 'dashboard' && filter.dashboard !== 'company'),
		},
	]
}
