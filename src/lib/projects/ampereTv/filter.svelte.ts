import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { AsyncFilter, type Iso3166Alpha2 } from '$lib/graphql/generated/gateway'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter } from '../common.filters.svelte'

export interface AmpereTvFilterProperties {
	iso: Iso3166Alpha2
	ampereTvBroadcaster: string
	revenue_type: string
	spend_type: string
}

export const ampereTvFilter = () => {
	const project = getProject()

	const ampereTvBroadcasterOptions = $derived.by(() => {
		if (!project.current || !project.filter?.iso) return [{ value: null, label: 'All' }]

		return AsyncFilter({
			variables: {
				projectId: project.current?._id,
				filter: {
					ampereTvBroadcasters: {
						iso: project.filter.iso,
					},
				},
			},
		}).then(({ data }) => [{ value: null, label: 'All' }, ...(data?.filter?.ampereTvBroadcasters || [])])
	})

	return [
		isoFilter(project),
		{
			key: 'ampereTvBroadcaster',
			type: FieldType.SELECT,
			props: {
				label: 'Broadcaster',
				placeholder: 'All',
				default: null,
				options: () => ampereTvBroadcasterOptions,
			},
			resetOn: ['iso'],
			hide: (filter: AmpereTvFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso === 'WW',
		},
		{
			key: 'revenue_type',
			type: FieldType.SELECT,
			props: {
				label: 'Revenue Type',
				placeholder: 'All',
				description: '(for Broadcaster Revenue only)',
				default: null,
				options: [
					{ value: null, label: 'All' },
					{ value: 'TV Advertising', label: 'TV Advertising' },
					{ value: 'Online Video Advertising', label: 'Online Video Advertising' },
				],
			},
			resetOn: ['iso', 'ampereTvBroadcaster'],
			hide: (filter: AmpereTvFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!!filter.ampereTvBroadcaster ||
				filter.iso === 'WW',
		},
		{
			key: 'spend_type',
			type: FieldType.SELECT,
			props: {
				label: 'Spend Type',
				placeholder: 'All',
				description: '(for Broadcaster Content Spend only)',
				default: null,
				options: [
					{ value: null, label: 'All' },
					{ value: 'Acquired Film & TV', label: 'Acquired Film & TV' },
					{ value: 'Original', label: 'Original' },
					{ value: 'Sports Rights', label: 'Sports Rights' },
					{ value: 'Other', label: 'Other' },
				],
			},
			resetOn: ['iso', 'ampereTvBroadcaster'],
			hide: (filter: AmpereTvFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!!filter.ampereTvBroadcaster ||
				filter.iso === 'WW',
		},
	]
}
