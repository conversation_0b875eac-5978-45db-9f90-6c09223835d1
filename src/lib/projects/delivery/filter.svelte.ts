import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { TimeUnit, type Iso3166Alpha2, AsyncProjectCities, DeliveryDataType } from '$lib/graphql/generated/gateway'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, providersFilter, timeUnitFilter } from '../common.filters.svelte'

const metroAreas: {
	[index: string]: Array<{ value: string; label: string }>
} = {
	GB: [
		// { value: 'Coventry', label: 'Coventry' },
		// { value: 'Belfast', label: 'Belfast' },
		{ value: 'Birmingham', label: 'Birmingham' },
		// { value: 'Bradford', label: 'Bradford' },
		// { value: 'Bristol', label: 'Bristol' },
		// { value: 'Leeds', label: 'Leeds' },
		{ value: 'Edinburgh', label: 'Edinburgh' },
		{ value: 'Glasgow', label: 'Glasgow' },
		{ value: 'Leeds', label: 'Leeds' },
		{ value: 'Leicester', label: 'Leicester' },
		{ value: 'Liverpool', label: 'Liverpool' },
		{ value: 'London', label: 'London' },
		{ value: 'Manchester', label: 'Manchester' },
		{ value: 'Nottingham', label: 'Nottingham' },
		{ value: 'Sheffield', label: 'Sheffield' },
		// { value: 'Southampton', label: 'Southampton' },
		{ value: 'Rest of UK', label: 'Rest of U.K.' },
	],
	AU: [
		{ value: 'Adelaide', label: 'Adelaide' },
		{ value: 'Perth', label: 'Perth' },
		{ value: 'Canberra', label: 'Canberra' },
		{ value: 'Brisbane', label: 'Brisbane' },
		{ value: 'Sydney', label: 'Sydney' },
		{ value: 'Melbourne', label: 'Melbourne' },
		{ value: 'Rest of AU', label: 'Rest of Australia' },
	],
	SA: [
		{ value: 'Jeddah', label: 'Jeddah' },
		{ value: 'Riyadh', label: 'Riyadh' },
		{ value: 'Rest of SA', label: 'Rest of S.A.' },
	],
	US: [
		{ value: 'Atlanta', label: 'Atlanta' },
		// { value: 'Austin', label: 'Austin' }, // annie
		// { value: 'Baltimore', label: 'Baltimore' }, // annie
		{ value: 'Boston', label: 'Boston' },
		// { value: 'Bronx', label: 'Bronx' },
		// { value: 'Brooklyn', label: 'Brooklyn' },
		// { value: 'Cincinnati', label: 'Cincinnati' }, // annie
		// { value: 'Cleveland', label: 'Cleveland' }, // annie
		// { value: 'Columbus', label: 'Columbus' }, // annie
		// { value: 'Charlotte', label: 'Charlotte' }, // annie
		{ value: 'Chicago', label: 'Chicago' },
		{ value: 'Dallas', label: 'Dallas' },
		// { value: 'Dallas-Fort Worth', label: 'Dallas-Fort Worth' }, // annie
		// { value: 'Detroid', label: 'Detroid' }, // annie
		// { value: 'Denver', label: 'Denver' }, // annie
		{ value: 'Houston', label: 'Houston' },
		// { value: 'Irvine', label: 'Irvine' }, // annie
		// { value: 'Indianapolis', label: 'Indianapolis' }, // annie
		// { value: 'Kansas City', label: 'Kansas City' }, // annie
		// { value: 'Fort Lauderdale', label: 'Fort Lauderdale' },
		{ value: 'Los Angeles', label: 'Los Angeles' },
		// { value: 'Long Beach', label: 'Long Beach' }, // annie
		{ value: 'New York City', label: 'New York City' },
		// { value: 'Manhattan', label: 'Manhattan' },
		{ value: 'Miami', label: 'Miami' },
		// { value: 'Milwaukee', label: 'Milwaukee' }, // annie
		// { value: 'Minneapolis', label: 'Minneapolis' }, // annie
		// { value: 'Mesa', label: 'Mesa' }, // annie
		// { value: 'New Heaven', label: 'New Heaven' }, // annie
		// { value: 'Nashville', label: 'Nashville' }, // annie
		// { value: 'Orlando', label: 'Orlando' }, // annie
		// { value: 'Pittsburgh', label: 'Pittsburgh' }, // annie
		{ value: 'Philadelphia', label: 'Philadelphia' },
		{ value: 'Phoenix', label: 'Phoenix' },
		// { value: 'Queens', label: 'Queens' },
		// { value: 'Portland', label: 'Portland' }, // annie
		// { value: 'Raleigh', label: 'Raleigh' }, // annie
		// { value: 'San Antonio', label: 'San Antonio' }, // annie
		// { value: 'San Diego', label: 'San Diego' }, // annie
		// { value: 'San Jose', label: 'San Jose' }, // annie
		// { value: 'St. Louis', label: 'St. Louis' }, // annie
		{ value: 'San Francisco', label: 'San Francisco' },
		// { value: 'Sacramento', label: 'Sacramento' }, // annie
		// { value: 'Seattle', label: 'Seattle' }, // annie
		// { value: 'Tampa', label: 'Tampa' }, // annie
		{ value: 'Washington, D.C.', label: 'Washington, D.C.' },
		{ value: 'Rest of US', label: 'Rest of U.S.' },
	],
	FR: [
		{ value: 'Lyon', label: 'Lyon' },
		{ value: 'Paris', label: 'Paris' },
		{ value: 'Rest of FR', label: 'Rest of France' },
	],
	KR: [
		{ value: 'Ansan', label: 'Ansan' },
		{ value: 'Busan', label: 'Busan' },
		{ value: 'Chuncheon', label: 'Chuncheon' },
		{ value: 'Daegu', label: 'Daegu' },
		{ value: 'Daejeon', label: 'Daejeon' },
		{ value: 'Gwangju', label: 'Gwangju' },
		{ value: 'Goyang', label: 'Goyang' },
		{ value: 'Jeju', label: 'Jeju' },
		{ value: 'Jeonju', label: 'Jeonju' },
		{ value: 'Incheon', label: 'Incheon' },
		{ value: 'Pohang', label: 'Pohang' },
		{ value: 'Seongnam', label: 'Seongnam' },
		{ value: 'Seoul', label: 'Seoul' },
		{ value: 'Ulsan', label: 'Ulsan' },
		{ value: 'Wonju', label: 'Wonju' },
		{ value: 'Yeosu', label: 'Yeosu' },
		{ value: 'Rest of KR', label: 'Rest of S.K.' },
	],
	AR: [
		{ value: 'Buenos Aires', label: 'Buenos Aires' },
		{ value: 'Rest of AR', label: 'Rest of Argentina' },
	],
	AT: [
		{ value: 'Vienna', label: 'Vienna' },
		{ value: 'Rest of AT', label: 'Rest of Austria' },
	],
	BE: [
		{ value: 'Antwerpen', label: 'Antwerpen' },
		{ value: 'Brussels', label: 'Brussels' },
		{ value: 'Gent', label: 'Gent' },
		{ value: 'Rest of BE', label: 'Rest of Belgium' },
	],
	BG: [
		{ value: 'Sofia', label: 'Sofia' },
		{ value: 'Rest of BG', label: 'Rest of Bulgaria' },
	],
	BR: [
		{ value: 'Belo Horizonte', label: 'Belo Horizonte' },
		{ value: 'Brasilia', label: 'Brasilia' },
		{ value: 'Campinas', label: 'Campinas' },
		{ value: 'Porto Alegre', label: 'Porto Alegre' },
		{ value: 'Recife', label: 'Recife' },
		{ value: 'Rio de Janeiro', label: 'Rio de Janeiro' },
		{ value: 'Salvador', label: 'Salvador' },
		{ value: 'Sao Paulo', label: 'São Paulo' },
		{ value: 'Rest of BR', label: 'Rest of Brazil' },
	],
	CA: [
		{ value: 'Calgary', label: 'Calgary' },
		{ value: 'Edmonton', label: 'Edmonton' },
		{ value: 'Halifax', label: 'Halifax' },
		{ value: 'Hamilton', label: 'Hamilton' },
		{ value: 'Kitchener', label: 'Kitchener' },
		{ value: 'Montreal', label: 'Montreal' },
		{ value: 'Oshawa', label: 'Oshawa' },
		{ value: 'Ottawa', label: 'Ottawa' },
		{ value: 'Quebec', label: 'Quebec' },
		{ value: 'Toronto', label: 'Toronto' },
		{ value: 'Vancouver', label: 'Vancouver' },
		{ value: 'Windsor', label: 'Windsor' },
		{ value: 'Winnipeg', label: 'Winnipeg' },
		{ value: 'Rest of CA', label: 'Rest of Canada' },
	],
	CL: [
		{ value: 'Santiago', label: 'Santiago' },
		{ value: 'Rest of CL', label: 'Rest of Chile' },
	],
	CO: [
		{ value: 'Bogota', label: 'Bogota' },
		{ value: 'Medellin', label: 'Medellin' },
		{ value: 'Rest of CO', label: 'Rest of Colombia' },
	],
	CZ: [
		{ value: 'Brno', label: 'Brno' },
		{ value: 'Prague', label: 'Prague' },
		{ value: 'Rest of CZ', label: 'Rest of C.R.' },
	],
	DK: [
		{ value: 'Copenhagen', label: 'Copenhagen' },
		{ value: 'Rest of DK', label: 'Rest of Denmark' },
	],
	EG: [
		{ value: 'Alexandria', label: 'Alexandria' },
		{ value: 'Cairo', label: 'Cairo' },
		{ value: 'Rest of EG', label: 'Rest of Egypt' },
	],
	DE: [
		{ value: 'Berlin', label: 'Berlin' },
		{ value: 'Bremen', label: 'Bremen' },
		{ value: 'Cologne', label: 'Cologne' },
		{ value: 'Dresden', label: 'Dresden' },
		{ value: 'Duisburg', label: 'Duisburg' },
		{ value: 'Dusseldorf', label: 'Dusseldorf' },
		{ value: 'Essen', label: 'Essen' },
		{ value: 'Frankfurt', label: 'Frankfurt' },
		{ value: 'Hamburg', label: 'Hamburg' },
		{ value: 'Leipzig', label: 'Leipzig' },
		{ value: 'Mannheim', label: 'Mannheim' },
		{ value: 'Munich', label: 'Munich' },
		{ value: 'Nuremberg', label: 'Nuremberg' },
		{ value: 'Stuttgart', label: 'Stuttgart' },
		{ value: 'Rest of DE', label: 'Rest of Germany' },
	],
	GR: [
		{ value: 'Athens', label: 'Athens' },
		{ value: 'Rest of GR', label: 'Rest of Greece' },
	],
	HU: [
		{ value: 'Budapest', label: 'Budapest' },
		{ value: 'Rest of HU', label: 'Rest of Hungary' },
	],
	IE: [
		{ value: 'Dublin', label: 'Dublin' },
		{ value: 'Rest of IE', label: 'Rest of Ireland' },
	],
	IL: [
		{ value: 'Tel Aviv-Yafo', label: 'Tel Aviv-Yafo' },
		{ value: 'Rest of IL', label: 'Rest of Israel' },
	],
	IT: [
		{ value: 'Milan', label: 'Milan' },
		{ value: 'Naples', label: 'Naples' },
		{ value: 'Rome', label: 'Rome' },
		{ value: 'Turin', label: 'Turin' },
		{ value: 'Rest of IT', label: 'Rest of Italy' },
	],
	JP: [
		{ value: 'Fukuoka', label: 'Fukuoka' },
		{ value: 'Hiroshima', label: 'Hiroshima' },
		{ value: 'Kitakyushu', label: 'Kitakyushu' },
		{ value: 'Kobe', label: 'Kobe' },
		{ value: 'Kyoto', label: 'Kyoto' },
		{ value: 'Maebashi', label: 'Maebashi' },
		{ value: 'Matsuyama', label: 'Matsuyama' },
		{ value: 'Nagoya', label: 'Nagoya' },
		{ value: 'Niigata', label: 'Niigata' },
		{ value: 'Okayama', label: 'Okayama' },
		{ value: 'Osaka', label: 'Osaka' },
		{ value: 'Sendai', label: 'Sendai' },
		{ value: 'Shizuoka', label: 'Shizuoka' },
		{ value: 'Takamatsu', label: 'Takamatsu' },
		{ value: 'Tokyo', label: 'Tokyo' },
		{ value: 'Toyama', label: 'Toyama' },
		{ value: 'Tsu', label: 'Tsu' },
		{ value: 'Utsunomiya', label: 'Utsunomiya' },
		{ value: 'Yokohama', label: 'Yokohama' },
		{ value: 'Rest of JP', label: 'Rest of Japan' },
	],
	MX: [
		{ value: 'Aguascalientes', label: 'Aguascalientes' },
		{ value: 'Chihuahua', label: 'Chihuahua' },
		{ value: 'Guadalajara', label: 'Guadalajara' },
		{ value: 'Leon', label: 'Leon' },
		{ value: 'Mexico City', label: 'Mexico City' },
		{ value: 'Merida', label: 'Merida' },
		{ value: 'Monterrey', label: 'Monterrey' },
		{ value: 'Puebla', label: 'Puebla' },
		{ value: 'Queretaro', label: 'Queretaro' },
		{ value: 'San Luis Potosi', label: 'San Luis Potosi' },
		{ value: 'Tijuana', label: 'Tijuana' },
		{ value: 'Toluca', label: 'Toluca' },
		{ value: 'Zapopan', label: 'Zapopan' },
		{ value: 'Rest of MX', label: 'Rest of Mexico' },
	],
	MY: [
		{ value: 'Kuala Lumpur', label: 'Kuala Lumpur' },
		{ value: 'Rest of MY', label: 'Rest of Malaysia' },
	],
	NL: [
		{ value: 'Amsterdam', label: 'Amsterdam' },
		{ value: 'Arnhem', label: 'Arnhem' },
		{ value: 'Eindhoven', label: 'Eindhoven' },
		{ value: 'Groningen', label: 'Groningen' },
		{ value: 'Haarlem', label: 'Haarlem' },
		{ value: 'Rotterdam', label: 'Rotterdam' },
		{ value: 'The Hague', label: 'The Hague' },
		{ value: 'Utrecht', label: 'Utrecht' },
		{ value: 'Zwolle', label: 'Zwolle' },
		{ value: 'Rest of NL', label: 'Rest of Netherlands' },
	],
	NO: [
		{ value: 'Oslo', label: 'Oslo' },
		{ value: 'Rest of NO', label: 'Rest of Norway' },
	],
	NZ: [
		{ value: 'Auckland', label: 'Auckland' },
		{ value: 'Rest of NZ', label: 'Rest of N.Z.' },
	],
	PK: [
		{ value: 'Karachi', label: 'Karachi' },
		{ value: 'Lahore', label: 'Lahore' },
		{ value: 'Rest of PK', label: 'Rest of Pakistan' },
	],
	PH: [
		{ value: 'Cebu City', label: 'Cebu City' },
		{ value: 'Manila', label: 'Manila' },
		{ value: 'Quezon City', label: 'Quezon City' },
		{ value: 'Rest of PH', label: 'Rest of Philippines' },
	],
	RO: [
		{ value: 'Bucharest', label: 'Bucharest' },
		{ value: 'Rest of RO', label: 'Rest of Romania' },
	],
	SE: [
		{ value: 'Stockholm', label: 'Stockholm' },
		{ value: 'Rest of SE', label: 'Rest of Sweden' },
	],
	CH: [
		{ value: 'Zurich', label: 'Zurich' },
		{ value: 'Rest of CH', label: 'Rest of Switzerland' },
	],
	ES: [
		{ value: 'Barcelona', label: 'Barcelona' },
		{ value: 'Bilbao', label: 'Bilbao' },
		{ value: 'Madrid', label: 'Madrid' },
		{ value: 'Malaga', label: 'Malaga' },
		{ value: 'Seville', label: 'Seville' },
		{ value: 'Valencia', label: 'Valencia' },
		{ value: 'Zaragoza', label: 'Zaragoza' },
		{ value: 'Rest of ES', label: 'Rest of Spain' },
	],
	AE: [
		{ value: 'Dubai', label: 'Dubai' },
		{ value: 'Sharjah', label: 'Sharjah' },
		{ value: 'Rest of AE', label: 'Rest of U.A.E.' },
	],
	TH: [
		{ value: 'Bangkok', label: 'Bangkok' },
		{ value: 'Rest of TH', label: 'Rest of Thailand' },
	],
	TR: [
		{ value: 'Ankara', label: 'Ankara' },
		{ value: 'Istanbul', label: 'Istanbul' },
		{ value: 'Rest of TR', label: 'Rest of Turkey' },
	],
	TW: [
		{ value: 'Hsinchu', label: 'Hsinchu' },
		{ value: 'Kaohsiung', label: 'Kaohsiung' },
		{ value: 'Taichung', label: 'Taichung' },
		{ value: 'Tainan', label: 'Tainan' },
		{ value: 'Taipei', label: 'Taipei' },
		{ value: 'Rest of TW', label: 'Rest of Taiwan' },
	],
}

const subMetroAreas: {
	[index: string]: {
		[index: string]: Array<{ value: string; label: string }>
	}
} = {
	US: {
		'New York City': [
			{ value: 'Bronx', label: 'Bronx' },
			{ value: 'Brooklyn', label: 'Brooklyn' },
			{ value: 'Manhattan', label: 'Manhattan' },
			{ value: 'Queens', label: 'Queens' },
		],
		Boston: [
			{ value: 'Boston', label: 'Boston' },
			{ value: 'Cambridge', label: 'Cambridge' },
			{ value: 'Somerville', label: 'Somerville' },
		],
	},
}

export interface DeliveryFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	dataType: DeliveryDataType
	metro: string
	submetro: string
	targetCurrency: string
	city: string
	providers: string[]
	week: string
	tableToShow: string
}

export const deliveryFilter = () => {
	const project = getProject()

	const metroOptions = $derived.by(() => {
		if (project.filter?.iso && metroAreas[project.filter.iso]) {
			return [{ value: null, label: 'All' }, ...metroAreas[project.filter.iso]]
		}

		return [{ value: null, label: 'All' }]
	})

	const subMetroOptions = $derived.by(() => {
		if (!!subMetroAreas[project.filter.iso] && !!subMetroAreas[project.filter.iso][project.filter.metro]) {
			return [{ value: null, label: 'All' }, ...subMetroAreas[project.filter.iso][project.filter.metro]]
		}
		return [{ value: null, label: 'All' }]
	})

	const targetCurrencyOptions = $derived.by(async () => {
		const items = (await project.baseCurrencies)
			.map((item) => {
				return {
					value: item,
					label: item,
				}
			})
			.sort((a, b) => {
				return a.label.localeCompare(b.label)
			})
			.filter((item, idx, self) => {
				return self.map((item) => item.value).indexOf(item.value) === idx
			})

		return [{ value: 'Local', label: 'Local' }, ...items]
	})

	const cityOptions = $derived.by(async () => {
		if (project.filter?.iso) return []

		return AsyncProjectCities({
			variables: {
				projectId: project.current?._id,
				iso: project.filter.iso,
			},
		}).then(
			({ data }) =>
				data?.projectCities?.map((item) => ({ value: item.city, label: `${item.city} (${item.count})` })) || [],
		)
	})

	return [
		isoFilter(project),
		timeUnitFilter(),
		// {
		// 	key: 'dataType',
		// 	type: FieldType.SELECT,
		// 	props: {
		// 		label: 'Delivery Type',
		// 		placeholder: 'All',
		// 		default: 'restaurant',
		// 		options: [
		// 			{ value: 'restaurant', label: 'Restaurants' },
		// 			{ value: 'grocery', label: 'Groceries' },
		// 		],
		// 	},
		// 	resetOn: ['iso'],
		// 	hide: () => page.route?.id === ('project/[projectId]/table' as RouteId),
		// },
		{
			key: 'metro',
			type: FieldType.SELECT,
			props: {
				label: 'Metropolitan Area',
				description: '(for Restaurants and Active Users)',
				placeholder: 'All',
				default: null,
				options: () => metroOptions,
			},
			resetOn: ['iso'],
			hide: (filter: DeliveryFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso ||
				// (filter.iso !== 'United States' && filter.iso !== 'United Kingdom') ||
				!Object.keys(metroAreas).includes(filter.iso),
		},
		{
			key: 'submetro',
			type: FieldType.SELECT,
			props: {
				label: 'Metropolitan Sub-Area',
				description: '(for Restaurants and Active Users)',
				placeholder: 'All',
				default: null,
				options: () => subMetroOptions,
			},
			resetOn: ['iso', 'metro'],
			hide: (filter: DeliveryFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.iso ||
				!filter.metro ||
				!Object.keys(subMetroAreas).includes(filter.iso) ||
				!Object.keys(subMetroAreas[filter.iso]).includes(filter.metro || ''),
		},
		{
			key: 'targetCurrency',
			type: FieldType.SELECT,
			props: {
				label: 'Currency',
				description: '(for Average Delivery Fee and Average Minimum Order)',
				placeholder: 'All',
				default: 'EUR',
				options: () => targetCurrencyOptions,
			},
			hide: (filter: DeliveryFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
		},
		{
			key: 'city',
			type: FieldType.AUTOCOMPLETE,
			props: {
				label: 'City',
				placeholder: 'All',
				options: () => cityOptions,
			},
			hide: (filter: DeliveryFilterProperties) =>
				page.route?.id !== ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
		},
		providersFilter(project),
		// weekFilter(),
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				label: 'Show',
				default: 'all',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
	]
}
