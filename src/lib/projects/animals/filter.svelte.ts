import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import {
	AnimalsBusinessType,
	AnimalsStockType,
	type Iso3166Alpha2,
	type TimeUnit,
	type ProductType,
	AnimalsDealerType,
} from '$lib/graphql/generated/gateway'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, extraCardsFilter, timeUnitFilter } from '../common.filters.svelte'

export interface AnimalsFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	extraCardsByProvider: string
	stockType: AnimalsStockType
	condition: string
	dealerType: AnimalsDealerType
	isPaying: boolean
	listingCountRange: string
	state: string
	dataPresentation: string
	productType: ProductType
	businessType: AnimalsBusinessType
}

export const animalsFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		//verticalTypeFilter(),
		extraCardsFilter(project),
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Animal Type',
				placeholder: 'All',
				//		description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					...Object.values(AnimalsStockType)
						.map((value) => ({
							value,
							label: value.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())),
						}))
						.sort((a, b) => a.label.localeCompare(b.label)),
				],
			},
			hide: (filter: AnimalsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
		},
		{
			key: 'dealerType',
			type: FieldType.SELECT,
			props: {
				label: 'Seller Type',
				placeholder: 'All',
				//		description: 'Listings, Sellers',
				options: [
					{ value: null, label: 'All' },
					...Object.values(AnimalsDealerType)
						.map((value) => ({
							value,
							label: value.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())),
						}))
						.sort((a, b) => a.label.localeCompare(b.label)),
				],
			},
		},
		{
			key: 'isPaying',
			type: FieldType.SELECT,
			props: {
				label: 'Account Type',
				placeholder: 'Total',
				//description: 'Sellers',
				options: [
					{ value: null, label: 'All' },
					{ value: true, label: 'Paying' },
				],
			},
		},
		// {
		// 	key: 'listingCountRange',
		// 	type: FieldType.SELECT,
		// 	props: {
		// 		label: 'Listing Count',
		// 		placeholder: 'All',
		// 		description: 'Sellers',
		// 		options: [
		// 			{ value: null, label: 'All' },
		// 			{ value: '0-5', label: '0-5' },
		// 			{ value: '6-10', label: '6-10' },
		// 			{ value: '11-20', label: '11-20' },
		// 			{ value: '21-50', label: '21-50' },
		// 			{ value: '51-100', label: '51-100' },
		// 			{ value: '101-200', label: '101-200' },
		// 			{ value: '+200', label: '+200' },
		// 		],
		// 	},
		// 	resetOn: ['iso'],
		// 	hide: (filter: AnimalsFilterProperties) => page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length || filter.iso !== 'BR',
		// },
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: (filter: AnimalsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
		},
		{
			key: 'businessType',
			type: FieldType.SELECT,
			props: {
				label: 'Business Type',
				placeholder: 'All',
				//description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					...Object.values(AnimalsBusinessType)
						.map((value) => ({
							value,
							label: value.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())),
						}))
						.sort((a, b) => a.label.localeCompare(b.label)),
				],
			},
			hide: (filter: AnimalsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
		},
	]
}

// const test = filter.filter((item) => item.type === FieldType.SELECT).map((field) => {
// 	return {
// 		test: field.props.options
// 	}
// })

// const test2 = filter[0].props.options
