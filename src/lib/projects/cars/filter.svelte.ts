import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, timeUnitFilter, extraCardsFilter, providersFilter } from '../common.filters.svelte'

import type {
	TimeUnit,
	CarsStockType,
	ClassifiedsDataType,
	DealerType,
	Iso3166Alpha2,
	ProductType,
	CarsVehicleType,
} from '$lib/graphql/generated/gateway'

const statesIsoCountry2 = ['NO']
//const productTypeIsoCountry2 = ['DE', 'US', 'SE']
const dataPresentationProviders = ['finn.no']

export interface CarsFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	extraCardsByProvider: string
	stockType: CarsStockType
	dealerType: DealerType
	isPaying: boolean
	listingCountRange: string
	state: string
	dataPresentation: string
	dataType: ClassifiedsDataType
	providers: string[]
	week: string
	tableToShow: string
	verticalType: string
	condition: string
	productType: ProductType
	vehicleType: CarsVehicleType
}

export const carsFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Stock Type',
				placeholder: 'All',
				options: [
					{ value: null, label: 'All' },
					{ value: 'new', label: 'New' },
					{ value: 'used', label: 'Used' },
				],
			},
		},
		{
			key: 'dealerType',
			type: FieldType.SELECT,
			props: {
				label: 'Seller Type',
				placeholder: 'All',
				options: [
					{ value: null, label: 'All' },
					{ value: 'dealer', label: 'Professional' },
					{ value: 'private', label: 'Private' },
				],
			},
		},
		{
			key: 'isPaying',
			type: FieldType.SELECT,
			props: {
				label: 'Dealer Type',
				placeholder: 'Total',
				options: [
					{ value: null, label: 'All' },
					{ value: true, label: 'Paying' },
					// { value: false, label: 'Free' },
				],
			},
		},
		{
			key: 'listingCountRange',
			type: FieldType.SELECT,
			props: {
				label: 'Dealer Size',
				placeholder: 'All',
				default: null,
				options: [
					{ value: null, label: 'All' },
					{ value: '0-50', label: '0 - 50' },
					{ value: '51-100', label: '51 - 100' },
					{ value: '101-250', label: '101 - 250' },
					{ value: '+250', label: '+ 250' },
				],
			},
			resetOn: ['iso'],
			hide: (filter: CarsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!(filter.iso === 'US' || filter.iso === 'DE' || filter.iso === 'FR' || filter.iso === 'NO'),
		},
		{
			key: 'listingCountRange',
			type: FieldType.SELECT,
			props: {
				label: 'Dealer Size',
				placeholder: 'All',
				default: null,
				options: [
					{ value: null, label: 'All' },
					{ value: '1-10', label: '1 - 10' },
					{ value: '11-25', label: '11 - 25' },
					{ value: '26-50', label: '26 - 50' },
					{ value: '51-100', label: '51 - 100' },
					{ value: '101-250', label: '101 - 250' },
					{ value: '+250', label: '+ 250' },
				],
			},
			hide: (filter: CarsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !(filter.iso === 'DE'),
		},
		{
			key: 'vehicleType',
			type: FieldType.SELECT,
			props: {
				label: 'Vehicle Type',
				placeholder: 'All',
				default: null,
				options: [
					{ value: null, label: 'All' },
					{ value: 'boat', label: 'Boat' },
					{ value: 'car', label: 'Car' },
					{ value: 'caravan', label: 'Caravan' },
					{ value: 'commercial', label: 'Commercial' },
					{ value: 'motorcycle', label: 'Motorcycle' },
					{ value: 'other', label: 'Other' },
				],
			},
		},
		providersFilter(project),
		extraCardsFilter(project),
		{
			key: 'state',
			type: FieldType.SELECT,
			props: {
				label: 'County',
				placeholder: 'All',
				default: null,
				options: [
					{ value: null, label: 'All' },
					...[
						{ value: 'NO-42', label: 'Agder' },
						{ value: 'NO-34', label: 'Innlandet' },
						{ value: 'NO-15', label: 'Møre og Romsdal' },
						{ value: 'NO-18', label: 'Nordland' },
						{ value: 'NO-03', label: 'Oslo' },
						{ value: 'NO-11', label: 'Rogaland' },
						{ value: 'NO-21', label: 'Svalbard' },
						{ value: 'NO-50', label: 'Trøndelag' },
						{ value: 'NO-46', label: 'Vestland' },
						{ value: 'NO-20', label: 'Finnmark' },
						{ value: 'NO-19', label: 'Troms' },
						{ value: 'NO-02', label: 'Akershus' },
						{ value: 'NO-06', label: 'Buskerud' },
						{ value: 'NO-01', label: 'Østfold' },
						{ value: 'NO-08', label: 'Telemark' },
						{ value: 'NO-07', label: 'Vestfold' },
					].sort((a, b) => a.label.localeCompare(b.label)),
					// Deprecated 2024-01-01
					...[
						{ value: 'NO-54', label: 'Troms og Finnmark' },
						{ value: 'NO-38', label: 'Vestfold og Telemark' },
						{ value: 'NO-30', label: 'Viken' },
					].sort((a, b) => a.label.localeCompare(b.label)),
				],
			},
			resetOn: ['iso'],
			hide: (filter: CarsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !statesIsoCountry2.includes(filter.iso || ''),
		},
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: () => page.route?.id === ('project/[projectId]/table' as RouteId),
		},
		{
			key: 'dataType',
			type: FieldType.SELECT,
			props: {
				label: 'Data Type',
				default: 'inventory',
				options: [
					{ value: 'inventory', label: 'Listings' },
					{ value: 'dealers', label: 'Dealers' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				label: 'Show',
				default: 'all',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		// weekFilter(),
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: (filter: CarsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!filter.extraCardsByProvider ||
				!dataPresentationProviders.includes(filter.extraCardsByProvider),
		},
	]
}
