import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { TimeUnit, type Iso3166Alpha2, AsyncProjectCountries } from '$lib/graphql/generated/gateway'
import { FieldType, type Field } from '$lib/modules/Form'

import type { ProjectState } from '$lib/components/Project'

export const isoFilter = (project: ProjectState) => {
	const options = $derived.by(() => {
		if (!project.current) return []

		return AsyncProjectCountries({
			variables: {
				projectId: project.current?._id,
			},
		}).then(({ data }) => data?.projectCountries?.map((country) => ({ value: country.iso, label: country.name })))
	})

	return {
		key: 'iso',
		type: FieldType.SELECT,
		props: {
			label: 'Country',
			placeholder: 'All',
			required: true,
			options: () => options,
		},
	} satisfies Field<{
		iso: Iso3166Alpha2
	}>
}

export const timeUnitFilter = () =>
	({
		key: 'timeUnit',
		type: FieldType.SELECT,
		props: {
			label: 'Time Period',
			placeholder: 'All',
			default: TimeUnit.Weeks,
			options: [
				{ value: TimeUnit.Weeks, label: 'Weeks' },
				{ value: TimeUnit.Months, label: 'Months' },
			],
		},
	}) satisfies Field<{
		timeUnit: TimeUnit
	}>

export const verticalTypeFilter = (project: ProjectState) =>
	({
		key: 'verticalType',
		type: FieldType.SELECT,
		props: {
			label: 'Verticals',
			placeholder: 'All',
			options: project.filteredVerticals.then((verticals) => [{ value: undefined, label: 'All' }, ...verticals]),
		},
		hide: (filter: { iso?: string }) =>
			page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
	}) satisfies Field<{
		iso: Iso3166Alpha2
		verticalType: string
	}>

export const extraCardsFilter = (project: ProjectState) =>
	({
		key: 'extraCardsByProvider',
		type: FieldType.SELECT,
		props: {
			label: 'Extra cards',
			placeholder: 'None',
			// multiple: true,
			options: project.filteredProviders.then((providers) => [{ value: undefined, label: 'None' }, ...providers]),
		},
		resetOn: ['iso'],
		hide: (filter: { iso?: string }) =>
			page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
	}) satisfies Field<{
		iso: Iso3166Alpha2
		extraCardsByProvider: string
	}>

export const providersFilter = (project: ProjectState) =>
	({
		key: 'providers',
		type: FieldType.SELECT,
		props: {
			label: 'Providers',
			placeholder: 'All',
			multiple: true,
			default: [],
			options: project.filteredProviders,
		},
		resetOn: ['iso'],
		hide: (filter: { iso?: string }) =>
			page.route?.id !== ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
	}) satisfies Field<{
		iso: Iso3166Alpha2
		providers: string[]
	}>

// TODO: Fix when we need table view
// export const weekFilter = () => ({
// 	key: 'week',
// 	type: FieldType.SELECT,
// 	props: {
// 		label: 'Week',
// 		placeholder: 'All',
// 		description: '(starting on)',
// 		options: project.getData().pipe(
// 			map(
// 				(data) =>
// 					data?.deltaTime?.map((item) => ({
// 						value: item,
// 						label: formatDate(dateWeekStringToMoment(item), 'dd MMM yyyy', 'en')
// 					})) || []
// 			)
// 		)
// 	},
// 	hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId) as RouteId
// })

// export const monthFilter = () => ({
// 	key: 'month',
// 	type: FieldType.SELECT,
// 	props: {
// 		label: 'months',
// 		placeholder: 'All',
// 		description: '(starting on)',
// 		options: project.getData().pipe(
// 			map((data) => {
// 				return data?.deltaTime.map((item) => ({
// 					value: item,
// 					label: formatDate(dateMonthStringToMoment(item), 'dd MMM yyyy', 'en')
// 				}))
// 			})
// 		)
// 	},
// 	hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId) as RouteId
// })
