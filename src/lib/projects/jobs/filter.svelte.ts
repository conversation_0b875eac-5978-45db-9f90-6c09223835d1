import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, timeUnitFilter, extraCardsFilter, providersFilter } from '../common.filters.svelte'

import type {
	TimeUnit,
	Iso3166Alpha2,
	JobsDealerType,
	JobsStockType,
	ProductType,
} from '$lib/graphql/generated/gateway'

const productTypeIsoCountry2 = ['DE', 'NO']

export interface JobsFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	extraCardsByProvider: string
	stockType: JobsStockType
	dealerType: JobsDealerType
	isPaying: boolean
	listingCountRange: string
	state: string
	dataPresentation: string
	providers: string[]
	week: string
	tableToShow: string
	productType: ProductType
}

export const jobsFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		// {
		//   key: 'platform',
		//   type: FieldType.SELECT,
		//   props: {
		//     label: 'Platform',
		//     placeholder: 'All',
		//     description: '(for Data.ai integrations)',
		//   },
		//   hooks: {
		//     onInit(field) {
		//       if (!field.formControl.value || field.formControl.value === null || field.formControl.value?.length <= 0) {
		//         if (field.props.options instanceof Observable) {
		//           field.props.options.pipe(take(1)).subscribe((_countries) => {
		//             field.formControl.enable({ emitEvent: false })
		//             field.formControl.setValue(null)
		//           })
		//         }
		//       }

		//       const options = [
		//         { value: null, label: 'All' },
		//         { value: 'android', label: 'Android' },
		//         { value: 'ios', label: 'iOS' },
		//       ]

		//       field.props.options = formGroup.get('iso').valueChanges.pipe(
		//         startWith(formGroup.get('iso').value),
		//         distinctUntilChanged((prev, curr) => {
		//           if (prev !== curr) {
		//             field.formControl.reset()
		//           }

		//           return false
		//         }),
		//         map((_iso) => options),
		//         share({
		//           connector: () => new ReplaySubject(1),
		//           resetOnError: false,
		//           resetOnComplete: false,
		//           resetOnRefCountZero: false,
		//         })
		//       )
		//     },
		//   },
		//   hide: (filter) => page.route?.id === ('project/[projectId]/table' as RouteId),
		// },
		// {
		//   key: 'device',
		//   type: FieldType.SELECT,
		//   props: {
		//     label: 'Device',
		//     placeholder: 'All',
		//     description: '(for Data.ai integrations)',
		//   },
		//   hooks: {
		//     onInit(field) {
		//       const options = {
		//         ios: [
		//           { value: null, label: 'All' },
		//           { value: 'iphone', label: 'iPhone' },
		//           { value: 'ipad', label: 'iPad' },
		//         ],
		//         android: [
		//           { value: null, label: 'All' },
		//           { value: 'android_phone', label: 'Android Phone' },
		//           { value: 'android_tablet', label: 'Android Tablet' },
		//         ],
		//       }

		//       field.props.options = formGroup.get('platform').valueChanges.pipe(
		//         startWith(formGroup.get('platform').value),
		//         distinctUntilChanged((prev, curr) => {
		//           if (prev !== curr) {
		//             field.formControl.reset()
		//           }

		//           return false
		//         }),
		//         map((platform) => {
		//           if (!platform) {
		//             field.formControl.disable()
		//             return []
		//           }
		//           field.formControl.enable()
		//           return options[platform]
		//         }),
		//         share({
		//           connector: () => new ReplaySubject(1),
		//           resetOnError: false,
		//           resetOnComplete: false,
		//           resetOnRefCountZero: false,
		//         })
		//       )
		//     },
		//   },
		//   hide: (_model) => isTable,
		// },
		providersFilter(project),
		// weekFilter(),
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				label: 'Show',
				default: 'all',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		extraCardsFilter(project),
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Job Type',
				placeholder: 'All',
				description: 'Offerings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'part_time', label: 'Part Time' },
					{ value: 'full_time', label: 'Full Time' },
					{ value: 'management', label: 'Management' },
					{ value: 'other', label: 'Others' },
				],
			},
		},
		{
			key: 'dealerType',
			type: FieldType.SELECT,
			props: {
				label: 'Publisher Type',
				placeholder: 'All',
				description: 'Offerings, Publishers',
				options: [
					{ value: null, label: 'All' },
					{ value: 'private', label: 'Private' },
					{ value: 'company', label: 'Company' },
					{ value: 'recruiter', label: 'Recruiter' },
				],
			},
		},
		{
			key: 'isPaying',
			type: FieldType.SELECT,
			props: {
				label: 'Account Type',
				placeholder: 'Total',
				description: 'Publishers',
				options: [
					{ value: null, label: 'All' },
					{ value: true, label: 'Paying' },
				],
			},
		},
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: (filter: JobsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!productTypeIsoCountry2.includes(filter.iso || ''),
		},
	]
}
