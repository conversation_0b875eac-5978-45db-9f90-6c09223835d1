import { accountingCards, accountingDefaultConfig, accountingFilter } from './accounting'
import { ampereTvCards, ampereTvDefaultConfig, ampereTvFilter } from './ampereTv'
import { animalsCards, animalsDefaultConfig, animalsFilter } from './animals'
import { auctionsCards, auctionsDefaultConfig, auctionsFilter } from './auctions'
import { carsCards, carsDefaultConfig, carsFilter } from './cars'
import { databasesCards, databasesDefaultConfig, databasesFilter } from './databases'
import { deliveryCards, deliveryDefaultConfig, deliveryFilter } from './delivery'
import { jobsCards, jobsDefaultConfig, jobsFilter } from './jobs'
import { marketplacesCards, marketplacesDefaultConfig, marketplacesFilter } from './marketplaces'
import { paymentsCards, paymentsDefaultConfig, paymentsFilter } from './payments'
import {
	realestateMarketplacesCards,
	realestateMarketplacesFilter,
	realestateMarketplacesDefaultConfig,
	realestateMarketplacesColors,
} from './realestatemarketplaces'
import { realestatesCards, realestatesDefaultConfig, realestatesFilter } from './realestates'
import { saasCards, saasDefaultConfig, saasFilter } from './saas'
import { vodCards, vodDefaultConfig, vodFilter } from './vod'

import type { DefaultProjectConfig, ProjectCard } from '$lib/components/Project'

export const cardsMap = {
	accounting: accountingCards,
	ampere: ampereTvCards, // TODO: remove this after cleaning the crawls file
	ampereTv: ampereTvCards,
	auctions: auctionsCards,
	cars: carsCards,
	delivery: deliveryCards,
	jobs: jobsCards,
	marketplaces: marketplacesCards,
	payments: paymentsCards,
	realestatemarketplaces: realestateMarketplacesCards,
	realestates: realestatesCards,
	vod: vodCards,
	saas: saasCards,
	databases: databasesCards,
	animals: animalsCards,
}

export const filterMap = {
	accounting: accountingFilter,
	ampere: ampereTvFilter, // TODO: remove this after cleaning the crawls file
	ampereTv: ampereTvFilter,
	auctions: auctionsFilter,
	cars: carsFilter,
	delivery: deliveryFilter,
	jobs: jobsFilter,
	marketplaces: marketplacesFilter,
	payments: paymentsFilter,
	realestatemarketplaces: realestateMarketplacesFilter,
	realestates: realestatesFilter,
	vod: vodFilter,
	saas: saasFilter,
	databases: databasesFilter,
	animals: animalsFilter,
}

export const defaultConfigMap = {
	accounting: accountingDefaultConfig,
	ampere: ampereTvDefaultConfig, // TODO: remove this after cleaning the crawls file
	ampereTv: ampereTvDefaultConfig,
	auctions: auctionsDefaultConfig,
	cars: carsDefaultConfig,
	delivery: deliveryDefaultConfig,
	jobs: jobsDefaultConfig,
	marketplaces: marketplacesDefaultConfig,
	payments: paymentsDefaultConfig,
	realestatemarketplaces: realestateMarketplacesDefaultConfig,
	realestates: realestatesDefaultConfig,
	vod: vodDefaultConfig,
	saas: saasDefaultConfig,
	databases: databasesDefaultConfig,
	animals: animalsDefaultConfig,
}

export const colorsMap = {
	accounting: undefined,
	ampere: undefined, // TODO: remove this after cleaning the crawls file
	ampereTv: undefined,
	auctions: undefined,
	cars: undefined,
	delivery: undefined,
	jobs: undefined,
	marketplaces: undefined,
	payments: undefined,
	realestatemarketplaces: realestateMarketplacesColors,
	realestates: undefined,
	vod: undefined,
	saas: undefined,
	databases: undefined,
}

export function getProjectCards<T extends keyof typeof cardsMap>(type: T) {
	return cardsMap[type]() as ProjectCard[]
}

export function getProjectFilter<T extends keyof typeof filterMap>(type: T) {
	return filterMap[type]?.() as ReturnType<(typeof filterMap)[T]>
}

export function getProjectDefaultConfig<T extends keyof typeof cardsMap>(type: T): DefaultProjectConfig {
	return defaultConfigMap[type]
}
