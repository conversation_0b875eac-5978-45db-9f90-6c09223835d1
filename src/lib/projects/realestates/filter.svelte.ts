import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import {
	RealEstatesPropertyScope,
	RealEstatesStockType,
	type TimeUnit,
	type ClassifiedsDataType,
	type Iso3166Alpha2,
	type RealEstatesBusinessType,
	type RealEstatesPropertyType,
} from '$lib/graphql/generated/gateway'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, timeUnitFilter, extraCardsFilter, providersFilter } from '../common.filters.svelte'

const stockTypesIsoCountry2 = ['FR', 'DE']
const productTypeIsoCountry2 = ['SE', 'US', 'AU', 'NO', 'DE', 'LT', 'LV', 'EE', 'CH']
const statesIsoCountry2 = ['SE', 'NO']

export interface RealestatesFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	extraCardsByProvider: string
	stockType: RealEstatesStockType
	dealerType: string
	isPaying: boolean
	stockTypes: RealEstatesStockType
	listingCountRange: string
	state: string
	dataPresentation: string
	providers: string[]
	week: string
	tableToShow: string
	dataType: ClassifiedsDataType
	productType: string
	businessType: RealEstatesBusinessType
	propertyType: RealEstatesPropertyType
	propertyScope: RealEstatesPropertyScope
	verticalType: string
	geoZone: string
}

export const realestatesFilter = () => {
	const project = getProject()

	const stockTypesOptions = $derived.by(() => {
		if (project.filter?.iso && stockTypesIsoCountry2.includes(project.filter.iso)) {
			return [
				{ value: null, label: 'All' },
				{ value: RealEstatesStockType.ResidentialSales, label: 'Residential Sales' },
				{ value: RealEstatesStockType.ResidentialLettings, label: 'Residential Lettings' },
				{ value: RealEstatesStockType.CommercialSales, label: 'Commercial Sales' },
				{ value: RealEstatesStockType.CommercialLettings, label: 'Commercial Lettings' },
				{ value: RealEstatesStockType.Other, label: 'Other' },
			]
		}
		return [{ value: null, label: 'All' }]
	})

	const productTypeOptions = $derived.by(() => {
		if (project.filter?.iso && productTypeIsoCountry2.includes(project.filter.iso)) {
			return [
				{ value: null, label: 'All' },
				{ value: 'BASIC', label: 'Basic' },
				{ value: 'PLUS', label: 'Plus' },
				{ value: 'PREMIUM', label: 'Premium' },
				{ value: 'MAX', label: 'Max' },
				{ value: 'ROCKET', label: 'Rocket' },
			]
		}

		return [{ value: null, label: 'All' }]
	})

	const stateOptions = $derived.by(() => {
		if (project.filter?.iso === 'SE') {
			return [
				{ value: null, label: 'All' },
				{ value: 'SE-K', label: 'Blekinge' },
				{ value: 'SE-W', label: 'Dalarna' },
				{ value: 'SE-I', label: 'Gotland' },
				{ value: 'SE-X', label: 'Gävleborg' },
				{ value: 'SE-N', label: 'Halland' },
				{ value: 'SE-Z', label: 'Jämtland' },
				{ value: 'SE-F', label: 'Jönköping' },
				{ value: 'SE-H', label: 'Kalmar' },
				{ value: 'SE-G', label: 'Kronoberg' },
				{ value: 'SE-BD', label: 'Norrbotten' },
				{ value: 'SE-M', label: 'Skåne' },
				{ value: 'SE-AB', label: 'Stockholm' },
				{ value: 'SE-D', label: 'Södermanland' },
				{ value: 'SE-C', label: 'Uppsala' },
				{ value: 'SE-S', label: 'Värmland' },
				{ value: 'SE-AC', label: 'Västerbotten' },
				{ value: 'SE-Y', label: 'Västernorrland' },
				{ value: 'SE-U', label: 'Västmanland' },
				{ value: 'SE-O', label: 'Västra Götaland' },
				{ value: 'SE-T', label: 'Örebro' },
				{ value: 'SE-E', label: 'Östergötland' },
			]
		}
		if (project.filter?.iso === 'NO') {
			return [
				{ value: null, label: 'All' },
				...[
					{ value: 'NO-42', label: 'Agder' },
					{ value: 'NO-34', label: 'Innlandet' },
					{ value: 'NO-15', label: 'Møre og Romsdal' },
					{ value: 'NO-18', label: 'Nordland' },
					{ value: 'NO-03', label: 'Oslo' },
					{ value: 'NO-11', label: 'Rogaland' },
					{ value: 'NO-21', label: 'Svalbard' },
					{ value: 'NO-50', label: 'Trøndelag' },
					{ value: 'NO-46', label: 'Vestland' },
					{ value: 'NO-20', label: 'Finnmark' },
					{ value: 'NO-19', label: 'Troms' },
					{ value: 'NO-02', label: 'Akershus' },
					{ value: 'NO-06', label: 'Buskerud' },
					{ value: 'NO-01', label: 'Østfold' },
					{ value: 'NO-08', label: 'Telemark' },
					{ value: 'NO-07', label: 'Vestfold' },
				].sort((a, b) => a.label.localeCompare(b.label)),
				// Deprecated 2024-01-01
				...[
					{ value: 'NO-54', label: 'Troms og Finnmark' },
					{ value: 'NO-38', label: 'Vestfold og Telemark' },
					{ value: 'NO-30', label: 'Viken' },
				].sort((a, b) => a.label.localeCompare(b.label)),
			]
		}
		if (project.filter?.iso === 'AT') {
			return [
				{ value: null, label: 'All' },
				{
					value: 'AT-1',
					label: 'Burgenland',
				},
				{
					value: 'AT-2',
					label: 'Kärnten',
				},
				{
					value: 'AT-3',
					label: 'Niederösterreich',
				},
				{
					value: 'AT-4',
					label: 'Oberösterreich',
				},
				{
					value: 'AT-5',
					label: 'Salzburg',
				},
				{
					value: 'AT-6',
					label: 'Steiermark',
				},
				{
					value: 'AT-7',
					label: 'Tirol',
				},
				{
					value: 'AT-8',
					label: 'Vorarlberg',
				},
				{
					value: 'AT-9',
					label: 'Wien',
				},
			].sort((a, b) => a.label.localeCompare(b.label))
		}
		return [{ value: null, label: 'All' }]
	})

	const geoZoneOptions = $derived.by(() => {
		if (project.filter?.iso === 'NO' && project.filter.extraCardsByProvider === 'finn.no') {
			return [
				{ value: null, label: 'All' },
				{ value: 'GEO1', label: 'GEO1' },
				{ value: 'GEO2', label: 'GEO2' },
				{ value: 'GEO3', label: 'GEO3' },
				{ value: 'GEO4', label: 'GEO4' },
			]
		}
		return [{ value: null, label: 'All' }]
	})

	return [
		isoFilter(project),
		timeUnitFilter(),
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Stock Type',
				placeholder: 'All',
				description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'residential_sales', label: 'Residential Sales' },
					{ value: 'residential_lettings', label: 'Residential Lettings' },
					{ value: 'commercial_sales', label: 'Commercial Sales' },
					{ value: 'commercial_lettings', label: 'Commercial Lettings' },
					{ value: 'other', label: 'Others' },
				],
			},
		},
		{
			key: 'businessType',
			type: FieldType.SELECT,
			props: {
				label: 'Business Type',
				placeholder: 'All',
				options: [
					{ value: null, label: 'All' },
					{ value: 'sale', label: 'Sale' },
					{ value: 'rent', label: 'Rent' },
				],
			},
			hide: (filter: { iso?: string }) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
		},
		{
			key: 'propertyScope',
			type: FieldType.SELECT,
			props: {
				label: 'Property Scope',
				placeholder: 'All',
				options: [
					{ value: null, label: 'All' },
					...[
						RealEstatesPropertyScope.Residential,
						RealEstatesPropertyScope.Commercial,
						RealEstatesPropertyScope.Land,
						RealEstatesPropertyScope.Other,
					].map((item) => ({
						value: item,
						label: item.charAt(0).toUpperCase() + item.slice(1),
					})),
				],
			},
			hide: (filter: { iso?: string }) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
		},
		{
			key: 'dealerType',
			type: FieldType.SELECT,
			props: {
				label: 'Seller Type',
				placeholder: 'All',
				description: 'Listings, New Listings and Agents',
				options: [
					{ value: null, label: 'All' },
					{ value: 'dealer', label: 'Professional' },
					{ value: 'private', label: 'Private' },
				],
			},
		},
		{
			key: 'isPaying',
			type: FieldType.SELECT,
			props: {
				label: 'Agent Type',
				placeholder: 'Total',
				description: 'Agents',
				options: [
					{ value: null, label: 'All' },
					{ value: true, label: 'Paying' },
				],
			},
		},
		{
			key: 'stockTypes',
			type: FieldType.SELECT,
			props: {
				label: 'Agent Stock Type',
				placeholder: 'All',
				default: null,
				options: () => stockTypesOptions,
			},
			resetOn: ['iso'],
			hide: (filter: RealestatesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!stockTypesIsoCountry2.includes(filter.iso || ''),
		},
		providersFilter(project),
		// weekFilter(),
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				label: 'Show',
				default: 'all',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		{
			key: 'dataType',
			type: FieldType.SELECT,
			props: {
				label: 'Data Type',
				default: 'inventory',
				options: [
					{ value: 'inventory', label: 'Listings' },
					{ value: 'dealers', label: 'Agents' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		extraCardsFilter(project),
		{
			key: 'productType',
			type: FieldType.SELECT,
			props: {
				label: 'Product Type',
				description: 'Listings',
				placeholder: 'All',
				default: null,
				options: () => productTypeOptions,
			},
			hide: (filter: RealestatesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.iso !== 'SE', //|| !productTypeIsoCountry2.includes(filter.iso),
		},
		{
			key: 'state',
			type: FieldType.SELECT,
			props: {
				label: 'County',
				description: 'Listings, Listings Income, New Production and Types',
				placeholder: 'All',
				default: null,
				options: () => stateOptions,
			},
			resetOn: ['iso'],
			hide: (filter: RealestatesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !statesIsoCountry2.includes(filter.iso || ''),
		},
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: (filter: RealestatesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) ||
				!productTypeIsoCountry2.includes(filter.iso || ''),
		},
		{
			key: 'geoZone',
			type: FieldType.SELECT,
			props: {
				label: 'GeoZone',
				//description: 'Listings, Listings Income, New Production and Types',
				placeholder: 'All',
				default: null,
				options: () => geoZoneOptions,
			},
			resetOn: ['iso', 'extraCardsByProvider'],
			hide: (filter: RealestatesFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || filter.extraCardsByProvider !== 'finn.no',
		},
	]
}
