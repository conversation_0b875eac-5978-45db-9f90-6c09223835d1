import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, providersFilter, timeUnitFilter, verticalTypeFilter } from '../common.filters.svelte'

import type { TimeUnit, Iso3166Alpha2 } from '$lib/graphql/generated/gateway'

export interface DatabasesFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	verticalType: string
	marketSegment: string
	providers: string[]
	month: string
	tableToShow: string
	webpage: string
}

export const databasesFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		verticalTypeFilter(project),
		// {
		// 	key: 'marketSegment',
		// 	type: FieldType.SELECT,
		// 	props: {
		// 		label: 'Market Segment',
		// 		placeholder: 'All',
		// 		default: null,
		// 		options: [
		// 			{ value: null, label: 'All' },
		// 			...(databasesDefaultConfig.additionalFilters
		// 				?.find((filter) => filter.key === 'marketSegment')
		// 				?.entries.flatMap((entry) => {
		// 					if (!entry) return []

		// 					return {
		// 						label: entry.label,
		// 						value: entry.value,
		// 					}
		// 				})
		// 				.sort((a, b) => a.label.localeCompare(b.label))
		// 				.map((vertical) => ({
		// 					value: vertical.value,
		// 					label: vertical.label,
		// 				})) || []),
		// 		],
		// 	},
		// 	hide: (filter) => page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
		// },
		providersFilter(project),
		// monthFilter(),
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				label: 'Show',
				default: 'all',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		{
			key: 'webpage',
			type: FieldType.SELECT,
			props: {
				label: 'Webpage',
				placeholder: 'Total',
				default: 'main',
				options: [
					{ value: 'main', label: 'Main Page' },
					{ value: 'login', label: 'Login Page' },
				],
			},
			hide: () => page.route?.id === ('project/[projectId]/table' as RouteId),
		},
	]
}
