import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { Iso3166Alpha2, TimeUnit, AccountingDataType } from '$lib/graphql/generated/gateway'
import { FieldType } from '$lib/modules/Form/form.interface'

import { accountingDefaultConfig } from './default.config'
import {
	isoFilter,
	timeUnitFilter,
	verticalTypeFilter,
	extraCardsFilter,
	providersFilter,
} from '../common.filters.svelte'

export interface AccountingFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	verticalType: string
	extraCardsByProvider: string

	providers: string[]
	month: string
	tableToShow: string
	dataType: AccountingDataType
	marketSegment: string
	webpage: string
}

export const accountingFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		providersFilter(project),
		// {
		// 	key: 'month',
		// 	type: FieldType.SELECT,
		// 	props: {
		// 		label: 'months',
		// 		placeholder: 'All',
		// 		description: '(starting on)',
		// 		options: derived(
		// 			Analysis({
		// 				variables: {
		// 					projectId: page.params?.projectId,
		// 					analysis: {
		// 						integrationSnapshotStats: {
		// 							integration: 'dataAi',
		// 							timeUnit: TimeUnit.Months,
		// 							dataType: 'app'
		// 						}
		// 					}
		// 				}
		// 			}),
		// 			({ data }) => {
		// 				const stats = data?.analysis.integrationSnapshotStats

		// 				return stats?.interval.map((item) => ({
		// 					value: item,
		// 					label: formatDate(dateMonthStringToMoment(item).toDate(), 'dd MMM yyyy', 'en')
		// 				}))
		// 			}
		// 		)
		// 	},
		// 	hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId)
		// },
		{
			key: 'tableToShow',
			type: FieldType.SELECT,
			props: {
				default: 'all',
				label: 'Show',
				options: [
					{ value: 'all', label: 'All' },
					{ value: 'changed', label: 'New & Removed' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		{
			key: 'dataType',
			type: FieldType.SELECT,
			props: {
				default: 'app',
				label: 'Data Type',
				options: [
					{ value: 'app', label: 'Apps' },
					{ value: 'partner', label: 'Preffer Accounting Partners' },
				],
			},
			hide: () => page.route?.id !== ('project/[projectId]/table' as RouteId),
		},
		verticalTypeFilter(project),
		{
			key: 'marketSegment',
			type: FieldType.SELECT,
			props: {
				label: 'Market Segment',
				placeholder: 'All',
				default: undefined,
				options: [
					{ value: undefined, label: 'All' },
					...(accountingDefaultConfig.additionalFilters
						?.find((item) => item.key === 'marketSegment')
						?.entries.map((vertical) => ({
							value: vertical.value,
							label: vertical.label,
						})) || []),
				],
			},
			resetOn: ['iso'],
			hide: (filter: { iso?: string }) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso?.length,
		},
		{
			key: 'webpage',
			type: FieldType.SELECT,
			props: {
				label: 'Webpage',
				placeholder: 'Total',
				description: '(for Accounting only)',
				default: 'main',
				options: [
					{ value: 'main', label: 'Main Page' },
					{ value: 'login', label: 'Login Page' },
				],
			},
			hide: () => page.route?.id === ('project/[projectId]/table' as RouteId),
		},
		extraCardsFilter(project),
	]
}
