import { page } from '$app/state'
import type { RouteId } from '$app/types'

import { getProject } from '$lib/components/Project'
import { FieldType } from '$lib/modules/Form/form.interface'

import { isoFilter, extraCardsFilter, timeUnitFilter, verticalTypeFilter } from '../common.filters.svelte'

import type {
	TimeUnit,
	AuctionsAuctionType,
	AuctionsStockType,
	ClassifiedsListingCountRange,
	DealerType,
	Iso3166Alpha2,
	ProductType,
} from '$lib/graphql/generated/gateway'

export interface AuctionsFilterProperties {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	verticalType: string
	extraCardsByProvider: string
	bidType: string
	stockType: AuctionsStockType
	auctionType: AuctionsAuctionType
	dealerType: DealerType
	dataPresentation: string
	webpage: string
	marketSegment: string
	productType: ProductType
	condition: string
	state: string
	isPaying: boolean
	listingCountRange: ClassifiedsListingCountRange
}

export const auctionsFilter = () => {
	const project = getProject()

	return [
		isoFilter(project),
		timeUnitFilter(),
		verticalTypeFilter(project),
		{
			key: 'bidType',
			type: FieldType.SELECT,
			props: {
				label: 'Bid Type',
				placeholder: 'All',
				description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'current', label: 'Current' },
					{ value: 'entry', label: 'Entry' },
					{ value: 'no_bid', label: 'No Bid' },
				],
			},
			hide: (filter: AuctionsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
		},
		{
			key: 'stockType',
			type: FieldType.SELECT,
			props: {
				label: 'Listing Type',
				placeholder: 'All',
				description: 'Listings',
				options: [
					{ value: null, label: 'All' },
					{ value: 'art_and_antiques', label: 'Art & Antiques' },
					{ value: 'auto', label: 'Auto' },
					{ value: 'collectibles', label: 'Collectibles' },
					{ value: 'consumer', label: 'Consumer' },
					{ value: 'heavy_machinery', label: 'Heavy Machinery' },
					{ value: 'real_estate', label: 'Real Estate' },
					{ value: 'others', label: 'Others' },
				],
			},
			hide: (filter: AuctionsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
		},
		{
			key: 'auctionType',
			type: FieldType.SELECT,
			props: {
				label: 'Auction Type',
				placeholder: 'All',
				description: 'Listings, Auctions',
				options: [
					{ value: null, label: 'All' },
					{ value: 'immediate', label: 'Buy Now' },
					{ value: 'live', label: 'Live' },
					{ value: 'timed', label: 'Scheduled' },
				],
			},
			hide: (filter: AuctionsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso || filter.iso.length <= 0,
		},
		{
			key: 'dealerType',
			type: FieldType.SELECT,
			props: {
				label: 'Auctioneer Type',
				placeholder: 'All',
				description: 'Listings, Auctioneers',
				options: [
					{ value: null, label: 'All' },
					{ value: 'private', label: 'Private' },
					{ value: 'dealer', label: 'Professional' },
				],
			},
		},
		extraCardsFilter(project),
		{
			key: 'dataPresentation',
			type: FieldType.SELECT,
			props: {
				label: 'Data Presentation',
				default: 'relative',
				options: [
					{ value: 'relative', label: 'Relative' },
					{ value: 'absolute', label: 'Absolute' },
				],
			},
			hide: (filter: AuctionsFilterProperties) =>
				page.route?.id === ('project/[projectId]/table' as RouteId) || !filter.iso,
		},
	]
}
