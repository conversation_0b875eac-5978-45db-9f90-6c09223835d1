import { untrack } from 'svelte'

import { browser } from '$app/environment'

import {
	AsyncAnalysis,
	AsyncProjectBaseCurrencies,
	AsyncProjectProviders,
	AsyncProjectStatus,
	FrontendProjectType,
	IntegrationType,
	type Project,
} from '$lib/graphql/generated/gateway'
import {
	CardType,
	type Break,
	type CardNumber,
	type ChartCard,
	type ChartType,
	type Title,
} from '$lib/modules/Dashboard'
import { getProjectCards, getProjectDefaultConfig, getProjectFilter } from '$lib/projects'

import { defaultProjectCardConfig } from './data'
import { limitedProviders } from './utils'
import { formatData } from './utils.formatData'

import type { Analysis, ProjectCard, ProjectCardDefinition } from './project.interface'

export class ProjectState {
	// State
	current = $state<Project | undefined>(undefined)
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	filter = $state<any>()

	status = $derived.by(() => {
		if (!this.current) return undefined

		return AsyncProjectStatus({
			variables: {
				projectId: this.current._id,
			},
		}).then((res) => res.data?.projectStatus)
	})

	providers = $derived.by(() => {
		if (!this.current) return undefined

		return AsyncProjectProviders({
			variables: {
				projectId: this.current._id,
			},
		}).then((res) => res.data?.projectProviders)
	})

	baseCurrencies = AsyncProjectBaseCurrencies({}).then((res) => res.data?.projectBaseCurrencies || [])

	enterprises = $derived.by(async () => {
		const current = untrack(() => this.current)
		const providers = await this.providers
		const status = await this.status

		if (!current || !this.filter || !status || !providers) return []

		const projectType =
			current.type === FrontendProjectType.Realestatemarketplaces ? FrontendProjectType.Realestates : current.type

		const blacklistedProviders = new Set(current.config?.providerBlacklist || [])

		const providerMap = new Map(providers.map((provider) => [provider.provider, provider.iso]))

		return status
			.filter((el) => el.project === projectType && el.status !== 'External' && !blacklistedProviders.has(el.provider))
			.flatMap((item) => {
				const iso = providerMap.get(item.provider)
				if (!iso) return []
				return {
					value: item.provider,
					iso,
					label: `${iso} - ${item.providerName}`,
				}
			})
			.filter((item, index, self) => self.findIndex((t) => t.value === item.value) === index)
			.sort((a, b) => a.label.localeCompare(b.label))
	})

	projectProviders = $derived.by(async () => {
		const current = untrack(() => this.current)
		const providers = await this.providers
		const status = await this.status

		if (!current || !this.filter || !status || !providers) return []

		const validProviders = new Set(
			providers
				.filter((provider) => provider.iso === this.filter.iso)
				.flatMap((item) =>
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					(item.provider as any) instanceof RegExp
						? status.filter((el) => (item.provider as unknown as RegExp).test(el.provider)).map((el) => el.provider)
						: item.provider,
				),
		)

		const blacklistedProviders = new Set(current.config?.providerBlacklist || [])

		return status.filter(
			(el) => el.project === current?.type && validProviders.has(el.provider) && !blacklistedProviders.has(el.provider),
		)
	})

	projectEnterprises = $derived.by(async () => {
		const current = untrack(() => this.current)
		const providers = await this.providers
		const status = await this.status

		if (!current || !providers || !status) return []

		const projectType =
			current.type === FrontendProjectType.Realestatemarketplaces ? FrontendProjectType.Realestates : current.type

		const blacklistedProviders = new Set(current.config?.providerBlacklist || [])

		const providerMap = new Map(providers.map((provider) => [provider.provider, provider.iso]))

		return status
			.filter((el) => el.project === projectType && el.status !== 'External' && !blacklistedProviders.has(el.provider))
			.flatMap((item) => {
				const iso = providerMap.get(item.provider)
				if (!iso) return []
				return {
					value: item.provider,
					iso,
					label: `${iso} - ${item.providerName}`,
				}
			})
			.filter((item, index, self) => self.findIndex((t) => t.value === item.value) === index)
			.sort((a, b) => a.label.localeCompare(b.label))
	})

	filteredProviders = $derived.by(async () => {
		const current = untrack(() => this.current)
		const providers = await this.providers

		if (!current || !providers || !this.filter) return []

		const limitedProvidersSet = new Set(
			limitedProviders(
				getProjectDefaultConfig(current.type).additionalFilters ?? [],
				this.filter.verticalType,
				this.filter.marketSegment,
			),
		)

		return providers
			.filter(
				(el) =>
					!limitedProvidersSet.size ||
					limitedProvidersSet.has(el.provider) ||
					Array.from(limitedProvidersSet).some((y) => y instanceof RegExp && y.test(el.provider)),
			)
			.map((item) => ({
				value: item.provider,
				// label: item.providerName.replace(/_/g, ' '),
				label: item.provider.replace(/_/g, ' '),
			}))
			.filter((e, i, self) => self.findIndex((a) => a.label === e.label && a.value === e.value) === i)
			.sort((a, b) => a.label.localeCompare(b.label))
	})

	filteredVerticals = $derived.by(async () => {
		const current = untrack(() => this.current)
		const providers = await this.projectProviders

		if (!current || !providers) return []

		const config = getProjectDefaultConfig(current.type)
		const verticalTypeFilter = config.additionalFilters?.find((filter) => filter.key === 'verticalType')

		if (!verticalTypeFilter) return []

		const providerSet = new Set(providers.map((p) => p.provider))

		return verticalTypeFilter.entries
			.filter((entry) => entry?.providers?.some((provider) => providerSet.has(provider)))
			.map((entry) => ({
				value: entry.value,
				label: entry.label,
			}))
			.sort((a, b) => a.label.localeCompare(b.label))
	})

	fields = $derived.by(() => {
		if (!this.current) return []

		return getProjectFilter(this.current.type)
	})

	cards = $derived.by(() => {
		if (!this.current) return []

		const projectCards = this.current?.config?.cards ?? getProjectDefaultConfig(this.current.type).cards

		return projectCards
			.filter((item) => {
				switch (item.card) {
					case 'apptopiaActiveUsers':
					case 'apptopiaDownloads':
						return this.current?.organization?.integrations?.includes(IntegrationType.Apptopia)

					case 'dataAiActiveUsers':
					case 'dataAiDownloads':
						return this.current?.organization?.integrations?.includes(IntegrationType.DataAi)

					case 'similarWebUsers':
					case 'similarWebVisits':
					case 'similarWebPageviews':
					case 'similarWebProvider':
						return this.current?.organization?.integrations?.includes(IntegrationType.SimilarWeb)

					case 'oxfordDataplanWeeklyVolume':
					case 'oxfordDataplanWeeklyMobilityNaa':
					case 'oxfordDataplanMonthlyMobilityNaa':
					case 'oxfordDataplanMonthlyVolume':
					case 'oxfordDataplanMonthlyAdRevenue':
						return this.current?.organization?.integrations?.includes(IntegrationType.OxfordDataplan)

					case 'quarterlyBroadcasterRevenue':
					case 'quarterlyBroadcasterRevenueSplit':
					case 'quarterlyBroadcasterContentSpend':
					case 'quarterlyBroadcasterContentSpendSplit':
					case 'totalAdvertisingWW':
					case 'totalAdvertisingCountry':
					case 'totalAdvertisingCountryPercent':
					case 'tvAdvertisingWW':
					case 'displayAdvertisingWW':
					case 'onlineVideoAdvertisingWW':
					case 'mobileVideoAdvertisingWW':
					case 'otherDigitalAdvertisingWW':
					case 'otherAdvertisingWW':
					case 'yearlyBroadcasterRevenue':
					case 'yearlyBroadcasterRevenuePercent':
					case 'yearlyBroadcasterContentSpend':
					case 'yearlyBroadcasterContentSpendPercent':
						return this.current?.organization?.integrations?.includes(IntegrationType.Ampere)

					default:
						return true
				}
			})
			.map((item) => {
				if (item.card === 'break') {
					return {
						type: CardType.BREAK,
						...(item.hide && { hide: item.hide }),
						// border: true,
						preWrap: true,
					} satisfies Break
				} else if (item.card === 'title') {
					return {
						type: CardType.TITLE,
						title: item.title || undefined,
						...(item.description && { description: item.description }),
						...(item.hide && { hide: item.hide }),
					} satisfies Title
				}

				const card = getProjectCards(this.current!.type).find((card) => card.id === item.card) as
					| ChartCard<ChartType>
					| CardNumber
					| undefined

				if (!card) {
					return {
						type: CardType.TITLE,
						title: 'Card not found',
						description: `Card with id ${item.card} not found`,
					} satisfies Title
				}

				if (item.title) {
					card.header = { ...card.header, title: item.title }
				}

				if (item.description) {
					card.header = { ...card.header, description: item.description }
				}

				if (item.hide) {
					card.hide = item.hide
				}

				return {
					id: item.card,
					...card,
					// class: `
					// ${item.size?.width ? sizeMap.width[item.size.width] : sizeMap.width[card.size.width]}
					// ${item.size?.height ? sizeMap.height[item.size.height] : sizeMap.height[card.size.height]}
					// `,
				}
			})
	})

	generateCard<T extends Analysis>(definition: ProjectCardDefinition<T>) {
		const { analysis, ...rest } = definition

		const getter = $derived.by(() => {
			if (!this.current) return

			try {
				return AsyncAnalysis({
					variables: {
						projectId: this.current._id,
						analysis: {
							[analysis.type]: typeof analysis.filter === 'function' ? analysis.filter(this.filter) : analysis.filter,
						},
					},
				}).then(({ data }) =>
					analysis.formatData
						? analysis.formatData(this.filter, data?.analysis[analysis.type])
						: formatData(this, data?.analysis[analysis.type]),
				)
			} catch (error) {
				console.error(error)
				return
			}
		})

		return {
			...defaultProjectCardConfig,
			...rest,
			getter: () => getter,
		} as ProjectCard
	}
}

let instance: ProjectState | undefined = undefined
export function getProject() {
	if (!instance) {
		throw new Error('Project is not initialized.')
	}

	return instance
}
export function initProject() {
	if (browser && instance && process.env.NODE_ENV !== 'test') {
		console.warn('Project is already initialized.')
		return instance
	}

	return (instance = new ProjectState())
}
