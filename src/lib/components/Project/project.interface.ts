import type {
	CardConfig,
	ProjectCardType,
	ProjectAdditionalFilterEntry,
	Iso3166Alpha2,
	TimeUnit,
	AnalysisInput,
	AnalysisQuery,
} from '$lib/graphql/generated/gateway'
import type {
	CardNumber,
	ChartCard,
	ChartData,
	ChartType,
	CustomDashboardCard,
	DashboardCard,
} from '$lib/modules/Dashboard'
import type { Component } from 'svelte'

export type Analysis = keyof AnalysisInput & keyof AnalysisQuery['analysis']

export interface AnalysisCardConfig<T extends Analysis = Analysis> {
	type: T
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	filter: ((filter: any) => AnalysisInput[T]) | AnalysisInput[T]
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	formatData?: (filter: any, data: AnalysisQuery['analysis'][T]) => Promise<ChartData<ChartType> | undefined>
}

export type ProjectCardDefinition<T extends Analysis = Analysis> = (
	| Omit<CardNumber, 'getter'>
	| Omit<ChartCard<ChartType>, 'getter'>
	| CustomDashboardCard<Component>
) & {
	id?: ProjectCardType | `${ProjectCardType}`
	analysis: AnalysisCardConfig<T>
}

export type ProjectCard = DashboardCard & {
	id?: ProjectCardType | `${ProjectCardType}`
}

export type ProjectCardConfig = Omit<CardConfig, 'card'> & { card: ProjectCardType | `${ProjectCardType}` }

export type ProjectAdditionalFilter = {
	entries: ProjectAdditionalFilterEntry[]
	key: string
}

export type DefaultProjectConfig = {
	cards: ProjectCardConfig[]
	additionalFilters?: ProjectAdditionalFilter[]
}

export interface BaseFilter {
	iso: Iso3166Alpha2
	timeUnit: TimeUnit
	verticalType: string
	marketSegment: string
}

export interface BaseV2Filter {
	isos: Iso3166Alpha2[]
	timeUnit: TimeUnit
	verticalType: string
	marketSegment: string
}

export interface FilterEntry {
	value: string
	label: string
	providers: Array<string | RegExp>
}

export interface AdditionalFilter {
	key: string
	entries: FilterEntry[]
}
